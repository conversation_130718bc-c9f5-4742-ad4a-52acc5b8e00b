# 动画剧本生成系统使用指南

## 概述

这是一个完整的从小说文本到动画视频的自动化生成系统。系统将几百万字的小说自动转换为结构化的剧本，然后生成对应的音频、图片和视频内容。

## 系统架构

### 完整流程

```
小说文本 → 章节摘要 → 剧集结构 → 剧本生成 → 音频合成 → 图片生成 → 视频合成
```

### 各阶段详细说明

1. **阶段1: 章节摘要生成**
   - 输入: 原始小说文本文件 (`.txt`)
   - 处理: 使用 LLM 分析并生成结构化章节摘要
   - 输出: 章节摘要JSON文件

2. **阶段2: 剧集结构化**
   - 输入: 章节摘要
   - 处理: 分组、生成故事大纲、确定集数分配、生成剧本
   - 输出: 结构化剧本JSON文件 (`episode_XX.json`)

3. **阶段3: 音频生成**
   - 输入: 剧本文件 + 语音配置
   - 处理: 使用 Azure TTS 合成对话和旁白
   - 输出: 音频文件 (`.wav`) + 时间轴文件 (`_timing.json`)

4. **阶段4: 图片生成**
   - 输入: 剧本文件
   - 处理: 生成图片提示词 → 使用 ComfyUI 生成场景图片
   - 输出: 场景图片文件

5. **阶段5: 视频合成**
   - 输入: 时间轴文件 + 图片 + 音频
   - 处理: 图片动效 + 音视频同步
   - 输出: 最终视频文件 (`.mp4`)

## 快速开始

### 1. 环境准备

确保已安装所有依赖：
```bash
pip install -r requirements.txt
```

确保以下服务可用：
- Azure Speech Services (用于TTS)
- ComfyUI (用于图片生成)
- FFmpeg (用于视频处理)

### 2. 准备配置文件

复制并修改语音配置文件：
```bash
cp voice_config_example.json voice_config.json
# 根据需要修改角色语音配置
```

### 3. 运行完整流程

```bash
python generate_animation_drama.py \
    "2-animation-drama/raw_text/save_witch_whole.txt" \
    "save_witch_whole" \
    --voice_config "voice_config.json"
```

### 4. 查看状态

```bash
python generate_animation_drama.py \
    "2-animation-drama/raw_text/save_witch_whole.txt" \
    "save_witch_whole" \
    --voice_config "voice_config.json" \
    --status
```

## 高级用法

### 分阶段执行

只执行特定阶段：
```bash
# 只生成摘要和剧集
python generate_animation_drama.py \
    "novel.txt" "project_name" \
    --voice_config "voice_config.json" \
    --stages summary episodes

# 只生成音频
python generate_animation_drama.py \
    "novel.txt" "project_name" \
    --voice_config "voice_config.json" \
    --stages audio
```

### 强制重新生成

```bash
python generate_animation_drama.py \
    "novel.txt" "project_name" \
    --voice_config "voice_config.json" \
    --force
```

### 自定义输出目录

```bash
python generate_animation_drama.py \
    "novel.txt" "project_name" \
    --voice_config "voice_config.json" \
    --output_dir "/custom/output/path"
```

## 配置说明

### 语音配置文件格式

```json
{
  "narration": {
    "voice": "zh-CN-YunxiNeural",
    "default_style": "narration"
  },
  "characters": {
    "角色名": {
      "voice": {
        "voice": "zh-CN-YunyangNeural",
        "default_style": "calm",
        "styles": {
          "Neutral": "calm",
          "Cheerful": "cheerful",
          "Sad": "sad",
          "Angry": "angry"
        }
      }
    }
  }
}
```

### 支持的情绪风格

- `Neutral`: 中性
- `Cheerful`: 愉快
- `Sad`: 悲伤
- `Angry`: 愤怒
- `Nervous`: 紧张
- `Determined`: 坚定
- `Hopeful`: 希望

## 输出文件结构

```
2-animation-drama/episodes/project_name/
├── generation_state.json          # 生成状态文件
├── episode_01.json                # 剧本文件
├── episode_01.wav                 # 音频文件
├── episode_01_timing.json         # 时间轴文件
├── episode_01.mp4                 # 视频文件
├── episode_02.json
├── episode_02.wav
├── episode_02_timing.json
├── episode_02.mp4
└── images/                        # 图片目录
    ├── episode_01/
    └── episode_02/
```

## 故障排除

### 常见问题

1. **Azure TTS 认证失败**
   - 检查环境变量 `AZURE_SUBSCRIPTION_KEY` 和 `AZURE_REGION`
   - 确认 Azure Speech Services 配额

2. **ComfyUI 连接失败**
   - 确认 ComfyUI 服务运行在 `127.0.0.1:8188`
   - 检查工作流文件是否存在

3. **FFmpeg 错误**
   - 确认 FFmpeg 已正确安装并在 PATH 中
   - 检查视频编码参数

### 恢复中断的生成

系统会自动保存进度，重新运行相同命令即可从中断点继续：

```bash
# 系统会自动跳过已完成的阶段
python generate_animation_drama.py \
    "novel.txt" "project_name" \
    --voice_config "voice_config.json"
```

### 查看详细错误信息

```bash
# 查看状态文件中的错误信息
python generate_animation_drama.py \
    "novel.txt" "project_name" \
    --voice_config "voice_config.json" \
    --status | jq '.errors'
```

## 性能优化建议

1. **并行处理**: 可以同时为多个剧集生成音频和图片
2. **缓存利用**: 系统会自动跳过已生成的内容
3. **资源监控**: 注意 GPU 内存使用情况（图片生成阶段）
4. **网络稳定**: 确保 Azure API 调用的网络稳定性

## 扩展开发

### 添加新的语音角色

在 `voice_config.json` 中添加新角色配置：

```json
{
  "characters": {
    "新角色名": {
      "voice": {
        "voice": "zh-CN-VoiceName",
        "default_style": "calm",
        "styles": {
          "Neutral": "calm"
        }
      }
    }
  }
}
```

### 自定义图片生成

修改 `generate_image_prompts.py` 中的提示词生成逻辑，或调整 ComfyUI 工作流文件。

### 添加新的视频效果

在 `generate_video.py` 中添加新的视频效果处理逻辑。
