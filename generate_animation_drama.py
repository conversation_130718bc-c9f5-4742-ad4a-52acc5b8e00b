#!/usr/bin/env python3
"""
统一的动画剧本生成主控脚本
从小说文本到最终视频的完整流程管理
"""

import os
import sys
import json
import argparse
import logging
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import (
    ANIMATION_DRAMA_DIR,
    ANIMATION_OUTPUTS_DIR,
    ANIMATION_EPISODES_DIR,
    logger
)

class AnimationDramaGenerator:
    """动画剧本生成器主类"""

    def __init__(self, novel_path: str, project_name: str, output_dir: Optional[str] = None):
        self.novel_path = Path(novel_path)
        self.project_name = project_name
        self.output_dir = Path(output_dir) if output_dir else Path(ANIMATION_EPISODES_DIR) / project_name

        # 确保输出目录存在
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 设置各阶段的输出路径
        self.raw_text_dir = Path(ANIMATION_DRAMA_DIR) / "raw_text"
        self.raw_text_dir.mkdir(parents=True, exist_ok=True)

        # 状态文件路径
        self.state_file = self.output_dir / "generation_state.json"
        self.load_state()

        logger.info(f"初始化动画剧本生成器: {project_name}")
        logger.info(f"小说路径: {self.novel_path}")
        logger.info(f"输出目录: {self.output_dir}")

    def load_state(self):
        """加载生成状态"""
        if self.state_file.exists():
            with open(self.state_file, 'r', encoding='utf-8') as f:
                self.state = json.load(f)
        else:
            self.state = {
                "stages_completed": [],
                "current_stage": None,
                "episodes_generated": [],
                "last_update": None,
                "errors": []
            }

    def save_state(self):
        """保存生成状态"""
        self.state["last_update"] = datetime.now().isoformat()
        with open(self.state_file, 'w', encoding='utf-8') as f:
            json.dump(self.state, f, ensure_ascii=False, indent=2)

    def is_stage_completed(self, stage: str) -> bool:
        """检查阶段是否已完成"""
        return stage in self.state.get("stages_completed", [])

    def mark_stage_completed(self, stage: str):
        """标记阶段为已完成"""
        if stage not in self.state.get("stages_completed", []):
            self.state["stages_completed"].append(stage)
        self.state["current_stage"] = stage
        self.save_state()

    def run_command(self, cmd: List[str], stage: str) -> bool:
        """执行命令并处理错误"""
        try:
            logger.info(f"执行 {stage}: {' '.join(cmd)}")
            result = subprocess.run(
                cmd,
                check=True,
                capture_output=True,
                text=True,
                cwd=os.getcwd()
            )
            logger.info(f"{stage} 完成")
            return True
        except subprocess.CalledProcessError as e:
            error_msg = f"{stage} 失败: {e.stderr}"
            logger.error(error_msg)
            self.state["errors"].append({
                "stage": stage,
                "error": error_msg,
                "timestamp": datetime.now().isoformat()
            })
            self.save_state()
            return False

    def stage1_generate_summary(self, force: bool = False) -> bool:
        """阶段1: 生成章节摘要"""
        stage = "summary_generation"

        if not force and self.is_stage_completed(stage):
            logger.info(f"阶段1已完成，跳过摘要生成")
            return True

        logger.info("开始阶段1: 生成章节摘要")

        # 检查输入文件
        if not self.novel_path.exists():
            logger.error(f"小说文件不存在: {self.novel_path}")
            return False

        # 设置输出文件路径
        summary_output = self.raw_text_dir / f"{self.project_name}.json"

        # 执行摘要生成
        cmd = [
            "python", "generate_summary.py",
            str(self.novel_path),
            "--output", str(summary_output)
        ]

        if self.run_command(cmd, stage):
            self.mark_stage_completed(stage)
            return True
        return False

    def stage2_generate_episodes(self, force: bool = False) -> bool:
        """阶段2: 生成剧集"""
        stage = "episode_generation"

        if not force and self.is_stage_completed(stage):
            logger.info(f"阶段2已完成，跳过剧集生成")
            return True

        logger.info("开始阶段2: 生成剧集")

        # 检查输入文件
        summary_file = self.raw_text_dir / f"{self.project_name}.json"
        if not summary_file.exists():
            logger.error(f"章节摘要文件不存在: {summary_file}")
            return False

        # 执行剧集生成
        cmd = [
            "python", "generate_episodes.py",
            str(summary_file),
            "--output_dir", str(self.output_dir)
        ]

        if self.run_command(cmd, stage):
            self.mark_stage_completed(stage)
            return True
        return False

    def get_episode_files(self) -> List[Path]:
        """获取所有剧集文件"""
        episode_files = []
        for file_path in self.output_dir.glob("episode_*.json"):
            # 排除timing文件
            if "_timing" not in file_path.name:
                episode_files.append(file_path)
        return sorted(episode_files)

    def stage3_generate_audio(self, voice_config: str, force: bool = False) -> bool:
        """阶段3: 生成音频"""
        stage = "audio_generation"

        if not force and self.is_stage_completed(stage):
            logger.info(f"阶段3已完成，跳过音频生成")
            return True

        logger.info("开始阶段3: 生成音频")

        # 检查语音配置文件
        voice_config_path = Path(voice_config)
        if not voice_config_path.exists():
            logger.error(f"语音配置文件不存在: {voice_config_path}")
            return False

        # 获取所有剧集文件
        episode_files = self.get_episode_files()
        if not episode_files:
            logger.error("没有找到剧集文件")
            return False

        # 为每个剧集生成音频
        success_count = 0
        for episode_file in episode_files:
            episode_name = episode_file.stem
            audio_output = episode_file.parent / f"{episode_name}.wav"

            # 如果音频文件已存在且不强制重新生成，跳过
            if not force and audio_output.exists():
                logger.info(f"音频文件已存在，跳过: {audio_output}")
                success_count += 1
                continue

            cmd = [
                "python", "episode_tts.py",
                "--episode", str(episode_file),
                "--voice", str(voice_config_path),
                "--output", str(audio_output)
            ]

            if self.run_command(cmd, f"{stage}_{episode_name}"):
                success_count += 1
                # 记录已生成的剧集
                if episode_name not in self.state.get("episodes_generated", []):
                    self.state["episodes_generated"].append(episode_name)
                    self.save_state()
            else:
                logger.error(f"生成音频失败: {episode_file}")

        if success_count == len(episode_files):
            self.mark_stage_completed(stage)
            return True
        else:
            logger.warning(f"音频生成部分成功: {success_count}/{len(episode_files)}")
            return False

    def stage4_generate_images(self, force: bool = False) -> bool:
        """阶段4: 生成图片"""
        stage = "image_generation"

        if not force and self.is_stage_completed(stage):
            logger.info(f"阶段4已完成，跳过图片生成")
            return True

        logger.info("开始阶段4: 生成图片")

        # 获取所有剧集文件
        episode_files = self.get_episode_files()
        if not episode_files:
            logger.error("没有找到剧集文件")
            return False

        success_count = 0
        for episode_file in episode_files:
            episode_name = episode_file.stem

            # 1. 生成图片提示词
            cmd_prompts = [
                "python", "generate_image_prompts.py",
                "--episode", str(episode_file),
                "--output", str(episode_file)  # 会更新原文件
            ]

            if not self.run_command(cmd_prompts, f"generate_prompts_{episode_name}"):
                logger.error(f"生成图片提示词失败: {episode_file}")
                continue

            # 2. 生成图片
            image_dir = self.output_dir / "images" / episode_name
            image_dir.mkdir(parents=True, exist_ok=True)

            cmd_images = [
                "python", "generate_images.py",
                "--prompts", str(episode_file),
                "--output_dir", str(image_dir)
            ]

            if self.run_command(cmd_images, f"generate_images_{episode_name}"):
                success_count += 1
            else:
                logger.error(f"生成图片失败: {episode_file}")

        if success_count == len(episode_files):
            self.mark_stage_completed(stage)
            return True
        else:
            logger.warning(f"图片生成部分成功: {success_count}/{len(episode_files)}")
            return False

    def convert_timing_to_segments(self, timing_file: Path) -> Path:
        """将剧集时间轴文件转换为视频生成器期望的段落格式"""
        try:
            with open(timing_file, 'r', encoding='utf-8') as f:
                episode_data = json.load(f)

            segments = []
            segment_id = 1

            # 获取音频文件路径
            episode_name = timing_file.stem.replace("_timing", "")
            audio_file = timing_file.parent / f"{episode_name}.wav"

            # 遍历所有场景
            for scene in episode_data.get("s", []):
                scene_duration = scene.get("scene_end_time", 0) - scene.get("scene_start_time", 0)

                # 为每个场景创建一个段落，使用 generate_video.py 期望的格式
                segment = {
                    "segment_id": segment_id,
                    "assigned_media": {
                        "type": "anchor",  # 使用 anchor 类型，因为我们没有实际的图片文件
                        "filepath": None
                    },
                    "audio": {
                        "path": str(audio_file) if audio_file.exists() else "",
                        "duration": scene_duration
                    },
                    "paragraph_text": scene.get("environment", {}).get("image", ""),  # 场景描述作为文本
                    "start_time": scene.get("scene_start_time", 0),
                    "end_time": scene.get("scene_end_time", 0)
                }

                segments.append(segment)
                segment_id += 1

            # 保存转换后的文件
            segments_file = timing_file.parent / f"{timing_file.stem.replace('_timing', '_segments')}.json"
            with open(segments_file, 'w', encoding='utf-8') as f:
                json.dump(segments, f, ensure_ascii=False, indent=2)

            logger.info(f"转换完成: {timing_file} -> {segments_file}")
            return segments_file

        except Exception as e:
            logger.error(f"转换时间轴文件失败 {timing_file}: {e}")
            raise

    def stage5_generate_videos(self, force: bool = False) -> bool:
        """阶段5: 生成视频"""
        stage = "video_generation"

        if not force and self.is_stage_completed(stage):
            logger.info(f"阶段5已完成，跳过视频生成")
            return True

        logger.info("开始阶段5: 生成视频")

        # 获取所有剧集文件
        episode_files = self.get_episode_files()
        if not episode_files:
            logger.error("没有找到剧集文件")
            return False

        success_count = 0
        for episode_file in episode_files:
            episode_name = episode_file.stem
            timing_file = episode_file.parent / f"{episode_name}_timing.json"
            # generate_video.py 的输出文件名规则：将 _timing.json 替换为 _video.mp4
            video_output = episode_file.parent / f"{episode_name}_video.mp4"

            # 检查必要的输入文件
            if not timing_file.exists():
                logger.error(f"时间轴文件不存在: {timing_file}")
                continue

            # 如果视频文件已存在且不强制重新生成，跳过
            if not force and video_output.exists():
                logger.info(f"视频文件已存在，跳过: {video_output}")
                success_count += 1
                continue

            try:
                # 转换时间轴文件为段落格式
                segments_file = self.convert_timing_to_segments(timing_file)

                cmd = [
                    "python", "generate_video.py",
                    "--input", str(segments_file),
                    "--theme", self.project_name,
                    "--lang", "Chinese",
                    "--width", "720",  # 使用较低分辨率以避免 HeyGen API 限制
                    "--height", "480"
                ]

                if self.run_command(cmd, f"{stage}_{episode_name}"):
                    success_count += 1
                else:
                    logger.error(f"生成视频失败: {episode_file}")

            except Exception as e:
                logger.error(f"处理剧集 {episode_name} 时出错: {e}")

        if success_count == len(episode_files):
            self.mark_stage_completed(stage)
            return True
        else:
            logger.warning(f"视频生成部分成功: {success_count}/{len(episode_files)}")
            return False

    def run_full_pipeline(self, voice_config: str, force_all: bool = False,
                         stages: Optional[List[str]] = None) -> bool:
        """运行完整的生成流程"""
        logger.info("开始运行完整的动画剧本生成流程")

        # 定义所有阶段
        all_stages = [
            ("summary", self.stage1_generate_summary),
            ("episodes", self.stage2_generate_episodes),
            ("audio", lambda force: self.stage3_generate_audio(voice_config, force)),
            ("images", self.stage4_generate_images),
            ("videos", self.stage5_generate_videos)
        ]

        # 如果指定了特定阶段，只运行这些阶段
        if stages:
            all_stages = [(name, func) for name, func in all_stages if name in stages]

        success_count = 0
        for stage_name, stage_func in all_stages:
            logger.info(f"执行阶段: {stage_name}")
            try:
                if stage_func(force_all):
                    success_count += 1
                    logger.info(f"阶段 {stage_name} 完成")
                else:
                    logger.error(f"阶段 {stage_name} 失败")
                    if not force_all:
                        logger.error("流程中断，使用 --force 参数可忽略错误继续执行")
                        return False
            except Exception as e:
                logger.error(f"阶段 {stage_name} 出现异常: {e}")
                if not force_all:
                    return False

        logger.info(f"流程完成，成功执行 {success_count}/{len(all_stages)} 个阶段")
        return success_count == len(all_stages)

    def get_status(self) -> Dict:
        """获取当前状态"""
        episode_files = self.get_episode_files()

        status = {
            "project_name": self.project_name,
            "novel_path": str(self.novel_path),
            "output_dir": str(self.output_dir),
            "stages_completed": self.state.get("stages_completed", []),
            "current_stage": self.state.get("current_stage"),
            "episodes_found": len(episode_files),
            "episodes_generated": self.state.get("episodes_generated", []),
            "last_update": self.state.get("last_update"),
            "errors": self.state.get("errors", [])
        }

        # 检查各种文件的存在情况
        status["files"] = {
            "novel_exists": self.novel_path.exists(),
            "summary_exists": (self.raw_text_dir / f"{self.project_name}.json").exists(),
            "episodes": []
        }

        for episode_file in episode_files:
            episode_name = episode_file.stem
            episode_info = {
                "name": episode_name,
                "script_exists": episode_file.exists(),
                "audio_exists": (episode_file.parent / f"{episode_name}.wav").exists(),
                "timing_exists": (episode_file.parent / f"{episode_name}_timing.json").exists(),
                "video_exists": (episode_file.parent / f"{episode_name}_video.mp4").exists()
            }
            status["files"]["episodes"].append(episode_info)

        return status


def main():
    parser = argparse.ArgumentParser(description='动画剧本生成主控脚本')
    parser.add_argument('novel_path', help='小说文本文件路径')
    parser.add_argument('project_name', help='项目名称')
    parser.add_argument('--voice_config', required=True, help='语音配置文件路径')
    parser.add_argument('--output_dir', help='输出目录路径')
    parser.add_argument('--stages', nargs='+',
                       choices=['summary', 'episodes', 'audio', 'images', 'videos'],
                       help='指定要执行的阶段')
    parser.add_argument('--force', action='store_true', help='强制重新生成所有内容')
    parser.add_argument('--status', action='store_true', help='显示当前状态')

    args = parser.parse_args()

    # 创建生成器实例
    generator = AnimationDramaGenerator(
        novel_path=args.novel_path,
        project_name=args.project_name,
        output_dir=args.output_dir
    )

    # 如果只是查看状态
    if args.status:
        status = generator.get_status()
        print(json.dumps(status, ensure_ascii=False, indent=2))
        return

    # 运行生成流程
    success = generator.run_full_pipeline(
        voice_config=args.voice_config,
        force_all=args.force,
        stages=args.stages
    )

    if success:
        logger.info("所有阶段执行成功！")
        print("\n=== 生成完成 ===")
        print(f"项目: {args.project_name}")
        print(f"输出目录: {generator.output_dir}")

        # 显示最终状态
        status = generator.get_status()
        print(f"生成的剧集数量: {status['episodes_found']}")
        for episode in status['files']['episodes']:
            print(f"  {episode['name']}: 脚本✓ 音频{'✓' if episode['audio_exists'] else '✗'} 视频{'✓' if episode['video_exists'] else '✗'}")
    else:
        logger.error("生成流程未完全成功")
        sys.exit(1)


if __name__ == "__main__":
    main()
