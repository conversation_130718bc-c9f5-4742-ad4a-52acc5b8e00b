# image2clip.py
import subprocess
import os
import tempfile
import shutil
import logging
import time
import re
import random
from typing import List, Tuple, Optional, Union, Dict, Any

from config import VIDEO_RESOLUTION, VIDEO_BITRATE, VIDEO_FPS, VIDEO_PRESET, VIDEO_CRF, VIDEO_CODEC, VIDEO_AUDIO_CODEC, FFMPEG_BASE_PARAMS
from modules.nlp_model import get_nlp_model
from modules.video_effects import select_effect, apply_effect_for_image, CONFIG as EFFECTS_CONFIG

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO) # Explicitly set level for this logger

# 缩放起始倍数和结束倍数
DEFAULT_ZOOM_START = 1.0
DEFAULT_ZOOM_END = 1.2  # 放大到1.1倍

# 平移速度调整因子（单位：每秒平移的像素数）
# DEFAULT_PAN_SPEED_FACTOR = 200  # 从50降低到10，减少抖动

# 控制高级特效的使用频率
# ADVANCED_EFFECTS_PROBABILITY = 0.7  # 70%概率使用高级特效，30%概率使用原有效果

def segment_text(text: str, n: int) -> List[str]:
    """
    使用spaCy将文本分割成n个语义单元
    
    Args:
        text: 要分割的文本
        n: 目标单元数量
    
    Returns:
        List[str]: 分割后的语义单元列表
    """
    if not text.strip():
        return [""] * n
    
    # 获取适合当前文本的NLP模型
    nlp = get_nlp_model(text=text)
    doc = nlp(text)
    
    # 1. 基于spaCy的句子边界进行一级拆分
    sentences = [s.text.strip() for s in doc.sents if s.text.strip()]
    
    # 如果没有检测到句子（罕见情况），回退到简单的正则拆分
    if not sentences:
        sentences = re.split(r'(?<=[。！？.!?])\s*', text.strip())
        sentences = [s for s in sentences if s]
    
    # 2. 如果句子数 >= n，按句子数平均分配
    if len(sentences) >= n:
        base, rem = divmod(len(sentences), n)
        units, idx = [], 0
        for i in range(n):
            cnt = base + (1 if i < rem else 0)
            units.append(''.join(sentences[idx: idx+cnt]))
            idx += cnt
        return units
    
    # 3. 二级拆分：使用中英文常见标点进行更细粒度拆分
    parts = []
    for sent in sentences:
        # 中英文逗号、分号、冒号、顿号、破折号等
        sub_parts = re.split(r'[，,；;：:、·—–]\s*', sent)
        parts.extend([p.strip() for p in sub_parts if p.strip()])
    
    # 如果二级拆分后数量足够
    if len(parts) >= n:
        base, rem = divmod(len(parts), n)
        units, idx = [], 0
        for i in range(n):
            cnt = base + (1 if i < rem else 0)
            units.append('，'.join(parts[idx: idx+cnt]))  # 用逗号重新连接相邻部分
            idx += cnt
        return units
    
    # 4. 如果二级拆分后仍然不足
    if len(parts) < n:
        logger.warning(f"文本太短无法按语义分割成{n}个单元")
        
        # 判断是否是极短文本（少于8个字符或2个词）
        if len(text.strip()) < 8 or len(text.strip().split()) <= 2:
            logger.warning(f"文本过短({text})，建议减少图片数量")
            # 使用相同的文本填充所有单元
            return [text.strip()] * n
    
    # 5. 退回到等长划分（字符级）
    logger.warning(f"退回到等长字符切分")
    clean_text = text.strip()
    avg = len(clean_text) / n
    return [clean_text[int(i*avg): int((i+1)*avg)] for i in range(n)]

def compute_durations(units: List[str], total_duration: float) -> List[float]:
    """
    基于文本单元的长度计算每个单元的时长
    
    Args:
        units: 文本单元列表
        total_duration: 总时长
    
    Returns:
        List[float]: 每个单元对应的时长
    """
    # 使用字符数作为权重，对于中文更合适
    char_counts = [len(u) for u in units]
    total_chars = sum(char_counts) or 1
    
    # 按字符比例分配时长
    durations = [cc/total_chars * total_duration for cc in char_counts]
    
    # 修正累积误差
    acc = sum(durations)
    durations[-1] += (total_duration - acc)
    
    return durations

def calculate_balanced_durations(units: List[str], total_duration: float, lang: str = None) -> List[float]:
    """
    计算平衡的时长分配，确保每个单元的时长差异不超过20%
    
    Args:
        units: 文本单元列表
        total_duration: 总时长
        lang: 语言代码，用于决定使用字符数还是词数作为权重
    
    Returns:
        List[float]: 每个单元对应的平衡时长
    """
    if not units or len(units) == 0:
        return []
    
    # 如果只有一个单元，直接返回总时长
    if len(units) == 1:
        return [total_duration]
    
    # 根据语言选择权重计算方式
    if lang in ("zh", "ja", "ko"):
        # 中日韩用字符数作为权重
        weights = [len(u) for u in units]
    else:
        # 其他语言用词数作为权重
        weights = [len(u.split()) for u in units]
    
    # 确保权重不为零
    weights = [max(1, w) for w in weights]
    total_weight = sum(weights)
    
    # 计算初始时长
    initial_durations = [w / total_weight * total_duration for w in weights]
    logger.debug(f"初始时长分配: {initial_durations}")
    
    # 计算平均时长
    avg_duration = total_duration / len(units)
    min_allowed = avg_duration * 0.8  # 最小允许时长 (80%)
    max_allowed = avg_duration * 1.2  # 最大允许时长 (120%)
    
    # 检查是否需要调整
    need_adjustment = any(d < min_allowed or d > max_allowed for d in initial_durations)
    
    if need_adjustment:
        # 限制每个单元的时长在允许范围内
        adjusted_durations = []
        for d in initial_durations:
            if d < min_allowed:
                adjusted_durations.append(min_allowed)
            elif d > max_allowed:
                adjusted_durations.append(max_allowed)
            else:
                adjusted_durations.append(d)
        
        # 重新归一化以保持总时长不变
        current_total = sum(adjusted_durations)
        scale_factor = total_duration / current_total
        final_durations = [d * scale_factor for d in adjusted_durations]
        logger.debug(f"平衡后的时长 (差异<=20%): {final_durations}")
        return final_durations
    
    # 如果不需要调整，返回初始时长
    return initial_durations

def _make_clip_for_single_image(
    image_path: str,
    output_path: str,
    duration: float,
    fps: int = VIDEO_FPS,
    resolution: tuple = VIDEO_RESOLUTION,
    prev_effect_key: Optional[str] = None,
    use_center_crop_preprocess: bool = False
) -> Tuple[str, str]:
    """
    根据单个图片生成无声视频片段（应用高级或基础特效）
    
    Args:
        image_path: 图片路径
        output_path: 输出视频路径
        duration: 视频时长
        fps: 帧率
        resolution: 分辨率
        prev_effect_key: 上一个使用的特效键
        use_center_crop_preprocess: 是否使用中心裁剪进行预处理

    Returns:
        Tuple[str, str]: (输出视频路径, 使用的特效键)
    """
    # 创建一个临时目录，确保即使发生异常也会被清理
    temp_dir = None
    
    try:
        # 验证输入文件
        if not os.path.isfile(image_path):
            logger.error(f"图片文件不存在: {image_path}")
            raise FileNotFoundError(f"找不到图片文件：{image_path}")
        
        # 1. 获取图片尺寸 (用于特效选择)
        try:
            probestr = subprocess.check_output(
                ["ffprobe","-v","error","-select_streams","v:0",
                 "-show_entries","stream=width,height",
                 "-of","csv=p=0:s=x", image_path],
                text=True,
                stderr=subprocess.PIPE # 捕获错误输出
            ).strip()
            image_width, image_height = map(int, probestr.split('x'))
        except subprocess.CalledProcessError as e:
            logger.error(f"无法获取图片尺寸: {image_path}. ffprobe error: {str(e.stderr)}")
            raise
        except Exception as e:
            logger.error(f"获取图片尺寸时发生未知错误: {image_path}. Error: {str(e)}")
            raise

        # 2. 选择特效
        selected_effect_key, effect_config = select_effect(
            duration=duration,
            image_width=image_width,
            image_height=image_height,
            prev_effect=prev_effect_key
        )
        is_advanced = effect_config.get("is_advanced", False)

        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        
        # 3. 应用特效
        width, height = resolution
        if is_advanced:
            # --- 处理高级特效 ---
            logger.debug(f"应用高级特效 '{selected_effect_key}' 到图片 {image_path}")
            # 注意：apply_effect_for_image 现在需要 effect_key 和 temp_dir
            # 并且它自己会返回 (output_path, effect_key)
            result = apply_effect_for_image(
                effect_key=selected_effect_key,
                image_path=image_path,
                output_path=output_path,
                width=width,
                height=height,
                duration=duration,
                fps=fps,
                temp_dir=temp_dir # 传入 temp_dir
            )
            return result # 返回 (output_path, effect_key)
        else:
            # --- 处理基础 Panzoom 特效 ---
            logger.info(f"应用基础 Panzoom 特效 '{selected_effect_key}' 到图片 {image_path}")
            pan_speed_x = effect_config.get("pan_x", 0)
            pan_speed_y = effect_config.get("pan_y", 0)
            # 获取基础 panzoom 的 zoom 配置
            basic_cfg = EFFECTS_CONFIG.get('basic_panzoom', {})
            zoom_start = basic_cfg.get('zoom_start', DEFAULT_ZOOM_START)
            zoom_end = basic_cfg.get('zoom_end', DEFAULT_ZOOM_END)

            # --- 复用之前的预处理和 FFmpeg 构建逻辑 ---
            # 1. 预处理图片 (和之前一样)
            display_aspect_ratio = f"{width}:{height}"
            temp_image = os.path.join(temp_dir, 'processed_image.jpg')

            if use_center_crop_preprocess:
                preprocess_filter = (
                    "split[original][for_bg];"
                    f"[for_bg]scale={width*2}:{height*2}:force_original_aspect_ratio=increase,"
                    f"crop={width}:{height}:(in_w-{width})/2:(in_h-{height})/2[bg];"
                    f"[original]scale={width}:{height}:force_original_aspect_ratio=increase,"
                    f"crop={width}:{height}:(in_w-{width})/2:(in_h-{height})/2[fg];"
                    "[bg][fg]overlay=x=(W-w)/2:y=(H-h)/2"
                )
            else:
                preprocess_filter = (
                    "split[original][for_bg];"
                    f"[for_bg]scale={width*2}:{height*2}:force_original_aspect_ratio=increase,"
                    f"crop={width}:{height},"
                    f"gblur=sigma=20[bg];"
                    f"[original]scale={width}:{height}:force_original_aspect_ratio=decrease,"
                    f"pad={width}:{height}:(ow-iw)/2:(oh-ih)/2:color=black@0[fg];"
                    "[bg][fg]overlay=x=(W-w)/2:y=(H-h)/2"
                )

            preprocess_command = [
                'ffmpeg', '-y',
                '-i', image_path,
                '-vf', preprocess_filter,
                '-q:v', '2',
                '-frames:v', '1',
                '-update', '1',
                temp_image
            ]
            subprocess.run(preprocess_command, check=True, capture_output=True, text=True)

            # 2. 生成视频 (使用基础 panzoom 参数)
            total_frames = int(duration * fps)
            zoom_expr = f"{zoom_start}+({zoom_end}-{zoom_start})*on/{total_frames}" if total_frames > 0 else f"{zoom_start}"

            # 计算 x/y 表达式，处理 pan_speed
            x_pan_term = f"-{abs(pan_speed_x)}*on/{fps}" if pan_speed_x < 0 else f"+{pan_speed_x}*on/{fps}"
            y_pan_term = f"-{abs(pan_speed_y)}*on/{fps}" if pan_speed_y < 0 else f"+{pan_speed_y}*on/{fps}"

            x_expr = f"(iw-iw/zoom)/2{x_pan_term}" if pan_speed_x != 0 else f"(iw-iw/zoom)/2"
            y_expr = f"(ih-ih/zoom)/2{y_pan_term}" if pan_speed_y != 0 else f"(ih-ih/zoom)/2"

            # 放大倍数（例如4倍）用于 panzoom 以避免黑边
            scale_factor_for_panzoom = 4
            scale_width = width * scale_factor_for_panzoom
            scale_height = height * scale_factor_for_panzoom

            video_filter = (
                # 先放大保证 panzoom 有足够内容
                f"scale={scale_width}:{scale_height}:force_original_aspect_ratio=increase,"
                f"crop={scale_width}:{scale_height}:(in_w-{scale_width})/2:(in_h-{scale_height})/2,"
                # 应用 zoompan
                f"zoompan="
                f"z='{zoom_expr}':"
                f"x='{x_expr}':"
                f"y='{y_expr}':"
                f"d=1:"
                f"s={width}x{height},"
                f"setsar=1:1,"
                f"setdar={width}/{height},"
                # 最后才 trim 和设置 fps
                f"trim=duration={duration},"
                f"fps={fps},"
                f"format=yuv420p"
            )

            # 构建基础FFmpeg命令
            ffmpeg_command = [
                'ffmpeg', '-y',
                '-hide_banner',
                '-loglevel', 'error',
                '-stats',
                '-stats_period', '10',
                '-thread_queue_size', '512',
                '-loop', '1',
                '-i', temp_image,
                '-filter_complex', video_filter,
                '-c:v', VIDEO_CODEC,
                '-preset', VIDEO_PRESET,
                '-crf', str(VIDEO_CRF),
                '-r', str(fps), # 使用传入的 fps
                '-b:v', VIDEO_BITRATE,
                '-pix_fmt', 'yuv420p',
                '-aspect', display_aspect_ratio,
                '-movflags', '+faststart',
                '-g', str(fps * 2), # 使用传入的 fps
                '-threads', '0',
                output_path # 输出到最终路径
            ]

            logger.debug("执行基础 Panzoom FFmpeg 命令:\n" + " ".join(ffmpeg_command))
            start_time = time.time()
            subprocess.run(ffmpeg_command, check=True)
            logger.info(f"基础 Panzoom 片段完成 ({selected_effect_key})，耗时: {time.time() - start_time:.2f}秒")

            # 验证输出文件
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                logger.debug(f"基础特效输出文件: {output_path} (大小: {file_size/1024/1024:.2f}MB)")
            else:
                logger.debug(f"基础特效输出文件未生成: {output_path}")

            # 对于基础效果，也返回路径和特效键
            return output_path, selected_effect_key

    except Exception as e:
        logger.error(f"生成视频片段时出错: {str(e)}")
        logger.error(f"错误类型: {type(e).__name__}")
        if hasattr(e, '__traceback__'):
            import traceback
            logger.error("错误堆栈:\n" + "".join(traceback.format_tb(e.__traceback__)))
        raise
    finally:
        # 确保临时目录被清理，即使发生异常
        if temp_dir and os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir, ignore_errors=True)
                logger.debug(f"清理临时目录: {temp_dir}")
            except Exception as cleanup_err:
                logger.warning(f"清理临时目录时发生错误: {str(cleanup_err)}")

def generate_video_from_images(
    image_paths: list,
    output_path: str,
    audio_path: str,
    duration: float,
    use_crop: bool = False,
    *,  # 强制后续参数使用关键字
    text: str = None,                   # 新增：对应这一段的原始文字
    segment_durations: List[float] = None, # 新增：预计算好的每张图时长
    fps: int = VIDEO_FPS,
    resolution: tuple = VIDEO_RESOLUTION,
    crossfade: float = 0.8,          # 0 表示无转场
) -> str:
    """
    对一段文字的多张图片生成一条带音轨的总片段
    
    Args:
        image_paths: 图片路径列表
        output_path: 输出视频路径
        audio_path: 音频路径
        duration: 总视频时长
        use_crop: 是否使用中心裁剪预处理
        text: 原始文本内容，用于自动分割语义单元
        segment_durations: 预计算好的每个图片单元的时长
        fps: 帧率
        resolution: 分辨率
        crossfade: 转场时长（秒），0表示无转场
    
    Returns:
        str: 输出视频路径
    """
    n = len(image_paths)
    assert n > 0, "image_paths 不能为空"
    
    # 如果只有一张图片，直接调用单图片生成函数
    if n == 1:
        logger.info("只有一张图片，直接生成单张图片视频")
        return generate_video_from_image(
            image_path=image_paths[0],
            output_path=output_path,
            duration=duration,
            fps=fps,
            resolution=resolution,
            audio_path=audio_path,
            use_crop=use_crop
        )
    
    # —— 分配时长 —— 
    # 为了让带转场的最终无声视频长度刚好等于音频长度，
    # 先把各图总时长设为：audio_duration + crossfade*(n-1)
    total_alloc_duration = duration + crossfade * (n - 1)
    logger.debug(f"考虑 crossfade 影响，分配总时长: {duration} + {crossfade} * {n-1} = {total_alloc_duration}")
    
    # 安全检查: 时长是否足够
    if total_alloc_duration <= 0:
        logger.warning(f"计算的分配时长 {total_alloc_duration} 太短，图片数量 {n}，crossfade {crossfade}")
        # 安全调整：如果计算出的时长太短，则每张图片至少保留0.5秒
        total_alloc_duration = max(0.5 * n, duration * 2)
        logger.debug(f"调整后的分配总时长: {total_alloc_duration}")
    
    # —— 1) 如果用户预设了 segment_durations，则归一化 —— 
    durations = None
    if segment_durations:
        if len(segment_durations) == n:
            logger.debug("使用预先计算的语义单元时长，并归一化到分配总时长")
            total_pre = sum(segment_durations) or 1.0
            durations = [
                d / total_pre * total_alloc_duration
                for d in segment_durations
            ]
            logger.debug(f"归一化后时长: {durations}")
        else:
            logger.warning(
                f"预设时长长度({len(segment_durations)})与图片数量({n})不匹配，将忽略预设时长"
            )
            segment_durations = None
    
    # —— 2) 没有预设或预设无效，则基于文本自动分配 ——
    if durations is None and text:
        logger.debug("根据文本内容自动计算每个单元的时长")
        try:
            units = segment_text(text, n)
            if len(units) < n:
                logger.warning(f"文本过短无法分割为{n}个单元，将使用等分时长")
                # 回退到等分时长
                durations = [total_alloc_duration / n] * n
            else:
                # 使用新函数计算平衡的时长分配
                from langdetect import detect
                try:
                    lang = detect(text)
                    durations = calculate_balanced_durations(units, total_alloc_duration, lang)
                    logger.debug(f"基于语言({lang})的平衡时长计算: {durations}")
                except:
                    # 检测语言失败，使用字符数作为权重
                    logger.warning("语言检测失败，使用字符数作为权重")
                    durations = calculate_balanced_durations(units, total_alloc_duration)
                
                logger.debug(f"文本单元分割结果: {units}")
                logger.debug(f"计算的时长分配: {durations}")
        except Exception as e:
            logger.error(f"分割文本时出错: {str(e)}，将使用等分时长")
            # 回退到等分时长
            durations = [total_alloc_duration / n] * n
    
    # —— 3) 最终 fallback：等分时长 ——
    if durations is None:
        logger.debug("没有文本和预设时长，使用等分时长")
        durations = [total_alloc_duration / n] * n
    
    logger.debug(f"每单元时长: {durations}")
    logger.debug(f"分配时长总和: {sum(durations):.2f}s, 其中包含转场补偿 {crossfade} * {n-1}")
    
    tmp_dir = tempfile.mkdtemp()
    tmp_clips = []

    try:
        # —— 生成无声子片段 ——
        prev_effect_key: Optional[str] = None
        for idx, img in enumerate(image_paths):
            tmp_clip_path = os.path.join(tmp_dir, f"clip_{idx}.mp4")
            result = _make_clip_for_single_image(
                image_path=img,
                output_path=tmp_clip_path,
                duration=durations[idx],
                fps=fps,
                resolution=resolution,
                prev_effect_key=prev_effect_key,
                use_center_crop_preprocess=use_crop
            )
            
            # _make_clip_for_single_image 现在总是返回 (路径, 特效key)
            if isinstance(result, tuple) and len(result) == 2:
                actual_path, used_effect_key = result
                tmp_clips.append(actual_path)
                
                prev_effect_key = used_effect_key
                logger.info(f"图片 {idx} 应用了特效: {used_effect_key}")
            else:
                # 这不应该发生，因为函数签名已更改
                tmp_clips.append(result)
                logger.warning(f"_make_clip_for_single_image 返回值格式不符合预期: {result}")
                prev_effect_key = None

        # 计算宽高比，用于显示宽高比设置
        width, height = resolution
        display_aspect_ratio = f"{width}:{height}"

        # —— 拼接部分 ——
        concat_out = os.path.join(tmp_dir, "all_silent.mp4")
        if crossfade == 0:
            # 直接 concat - 注意：不能使用简单的文件拼接，因为会丢失宽高比信息
            # 需要重新编码并设置宽高比
            concat_list = os.path.join(tmp_dir, "list.txt")
            with open(concat_list, "w") as f:
                for p in tmp_clips:
                    f.write(f"file '{p}'\n")
            
            logger.debug("使用concat拼接无声视频片段，同时设置宽高比")
            
            # 不再简单地使用-c copy，而是使用滤镜链确保正确设置宽高比
            concat_cmd = [
                "ffmpeg", "-y", 
                "-hide_banner", "-loglevel", "error",
                "-f", "concat", "-safe", "0", "-i", concat_list,
                "-vf", f"setsar=1:1,setdar={width}/{height}",
                "-c:v", VIDEO_CODEC, "-preset", VIDEO_PRESET, "-crf", str(VIDEO_CRF),
                "-aspect", display_aspect_ratio,
                concat_out
            ]
            
            logger.debug("执行FFmpeg命令:\n" + " ".join(concat_cmd))
            subprocess.run(concat_cmd, check=True)
        else:
            # 使用xfade滤镜进行转场拼接
            logger.debug(f"使用xfade转场(时长={crossfade}秒)拼接无声视频片段")
            
            # —— 计算每次转场的 offset ——
            offsets = []
            t_acc = 0.0
            for d in durations[:-1]:
                t_acc += d               # 累加本段完整时长
                offsets.append(t_acc - crossfade)
                t_acc -= crossfade       # 从累加里"退回"一个 crossfade，准备下次累加
            
            # 构造 filter_complex
            inputs = []
            for p in tmp_clips:
                inputs.extend(["-i", p])
            
            # 如果N>1，则构建链式拼接
            if n > 1:
                filter_complex = ""
                
                # 构建xfade滤镜链（transition=fade）
                for i in range(n - 1):
                    left = f"[{i}:v]" 
                    right = f"[{i+1}:v]"
                    tmp_out = f"[v_tmp{i}]"
                    out = f"[v{i}]" if i < n-2 else "[v_tmp_final]"
                    
                    if i == 0:
                        # 第一次拼接
                        filter_complex += f"{left}{right}xfade=transition=fade:duration={crossfade}:offset={offsets[i]}{tmp_out};"
                        filter_complex += f"{tmp_out}setpts=PTS-STARTPTS{out};"
                    else:
                        # 后续拼接，左侧使用上一次的输出
                        prev_out = f"[v{i-1}]"
                        filter_complex += f"{prev_out}{right}xfade=transition=fade:duration={crossfade}:offset={offsets[i]}{tmp_out};"
                        filter_complex += f"{tmp_out}setpts=PTS-STARTPTS{out};"
                
                # 添加最终的宽高比设置
                filter_complex += f"[v_tmp_final]setsar=1:1,setdar={width}/{height}[vout]"
                
                # 构建完整的FFmpeg命令
                cmd = ["ffmpeg", "-y",'-hide_banner','-loglevel', 'error']  + inputs + [
                    "-filter_complex", filter_complex,
                    "-map", "[vout]", 
                    "-c:v", VIDEO_CODEC,
                    "-preset", VIDEO_PRESET, 
                    "-crf", str(VIDEO_CRF),
                    "-aspect", display_aspect_ratio,  # 添加宽高比设置
                    concat_out
                ]
                
                logger.debug("执行FFmpeg命令:\n" + " ".join(cmd))
                subprocess.run(cmd, check=True)
            else:
                # 如果只有一个片段（不应该发生）
                shutil.copy(tmp_clips[0], concat_out)

        # —— 合并音频 ——
        logger.debug("开始合并音频...")
        merge_start_time = time.time()
        
        # 获取音频精确时长        
        merge_command = [
            "ffmpeg", "-y",
            "-hide_banner",
            "-loglevel", "error",
            "-stats",
            "-stats_period", "10",
            "-i", concat_out,
            "-i", audio_path,
            "-map", "0:v",  # 确保明确映射视频流
            "-map", "1:a",  # 确保明确映射音频流
            "-c:v", "copy",  # 复制视频流
            "-c:a", VIDEO_AUDIO_CODEC,
            "-b:v", VIDEO_BITRATE,
            "-aspect", display_aspect_ratio,  # 确保输出视频有正确的宽高比
            "-strict", "experimental",
            "-shortest",  # 使用最短流长度，确保音视频自然对齐
            output_path
        ]
        
        subprocess.run(merge_command, check=True)
        logger.debug(f"音频合并完成，耗时: {time.time() - merge_start_time:.2f}秒")
        
        # 验证最终输出文件
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            logger.debug(f"最终输出文件: {output_path} (大小: {file_size/1024/1024:.2f}MB)")
        else:
            logger.error(f"最终输出文件未生成: {output_path}")
            
        return output_path
        
    except Exception as e:
        logger.error(f"生成多图视频时出错: {str(e)}")
        logger.error(f"错误类型: {type(e).__name__}")
        if hasattr(e, '__traceback__'):
            import traceback
            logger.error("错误堆栈:\n" + "".join(traceback.format_tb(e.__traceback__)))
        raise
    finally:
        # 清理临时目录
        shutil.rmtree(tmp_dir, ignore_errors=True)

# 修复 generate_video_from_image 函数，移除旧参数并使用新的特效逻辑
def generate_video_from_image(
    image_path: str,
    output_path: str,
    duration: float,
    fps: int = VIDEO_FPS,
    resolution: tuple = VIDEO_RESOLUTION,
    audio_path: str = None,
    # 以下参数不再使用，保留是为了向后兼容，但会发出警告
    effect_type: str = None,    # 不再使用
    zoom_start: float = None,   # 不再使用
    zoom_end: float = None,     # 不再使用
    image_index: int = None,    # 不再使用
    use_crop: bool = False
) -> str:
    """
    根据单个图片和音频生成视频片段
    
    特效选择现在由 modules.video_effects.select_effect 自动处理，
    不再需要手动指定 effect_type 等参数。
    
    Args:
        image_path: 图片路径
        output_path: 输出视频路径
        duration: 视频时长
        fps: 帧率
        resolution: 分辨率
        audio_path: 音频路径
        use_crop: 是否使用中心裁剪预处理
        
        effect_type: [废弃] 效果类型，"panzoom" 或 "zoom"
        zoom_start: [废弃] 缩放起始倍数
        zoom_end: [废弃] 缩放结束倍数 
        image_index: [废弃] 图片索引(用于决定平移方向)
        
    Returns:
        str: 输出视频路径
    """
    # 处理废弃参数警告
    if any(param is not None for param in [effect_type, zoom_start, zoom_end, image_index]):
        import warnings
        warnings.warn(
            "参数 effect_type, zoom_start, zoom_end, image_index 已废弃。"
            "特效选择现在由 modules.video_effects.select_effect 自动处理。",
            DeprecationWarning, stacklevel=2
        )
    
    try:
        # 验证输入文件
        if not os.path.isfile(image_path):
            logger.error(f"图片文件不存在: {image_path}")
            raise FileNotFoundError(f"找不到图片文件：{image_path}")
        
        if audio_path and not os.path.isfile(audio_path):
            logger.error(f"音频文件不存在: {audio_path}")
            raise FileNotFoundError(f"找不到音频文件：{audio_path}")
            
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建临时视频路径
            temp_video = os.path.join(temp_dir, 'temp_video.mp4')
            
            # 生成无声视频
            result = _make_clip_for_single_image(
                image_path=image_path,
                output_path=temp_video,
                duration=duration,
                fps=fps,
                resolution=resolution,
                prev_effect_key=None, # 单张图片没有上一个特效
                use_center_crop_preprocess=use_crop
            )
            
            # 处理返回值 (_make_clip_for_single_image 总是返回 tuple)
            temp_video, used_effect_key = result
            logger.info(f"单张图片应用特效: {used_effect_key}")
            
            # 计算正确的视频宽高比
            width, height = resolution
            display_aspect_ratio = f"{width}:{height}"
            
            if audio_path:
                # 合并音频
                merge_command = [
                    'ffmpeg', '-y',
                    '-hide_banner',
                    '-loglevel', 'error',
                    '-stats',
                    '-stats_period', '10',
                    '-i', temp_video,
                    '-i', audio_path,
                    '-c:v', 'copy',
                    '-c:a', VIDEO_AUDIO_CODEC,
                    '-b:v', VIDEO_BITRATE,
                    '-aspect', display_aspect_ratio,  # 确保输出视频有正确的宽高比
                    '-strict', 'experimental',
                    '-shortest',
                    output_path
                ]
                logger.debug("开始合并音频...")
                merge_start_time = time.time()
                subprocess.run(merge_command, check=True)
                logger.debug(f"音频合并完成，耗时: {time.time() - merge_start_time:.2f}秒")
            else:
                # 没有音频，直接复制视频
                shutil.copy(temp_video, output_path)
                logger.debug("没有音频，直接使用无声视频")
            
            # 验证输出文件
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                logger.debug(f"输出文件: {output_path} (大小: {file_size/1024/1024:.2f}MB)")
            else:
                logger.error(f"输出文件未生成: {output_path}")
            
        return output_path
        
    except Exception as e:
        logger.error(f"生成视频片段时出错: {str(e)}")
        logger.error(f"错误类型: {type(e).__name__}")
        if hasattr(e, '__traceback__'):
            import traceback
            logger.error("错误堆栈:\n" + "".join(traceback.format_tb(e.__traceback__)))
        raise

if __name__ == "__main__":
    # 示例用法
    image_path = "example.jpg"
    output_path = "output.mp4"
    duration = 10.0
    
    try:
        result = generate_video_from_image(
            image_path=image_path,
            output_path=output_path,
            duration=duration,
            effect_type="panzoom"
        )
        print(f"视频生成成功：{result}")
    except Exception as e:
        print(f"视频生成失败：{str(e)}")