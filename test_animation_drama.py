#!/usr/bin/env python3
"""
动画剧本生成系统测试脚本
用于验证现有流程和发现问题
"""

import os
import sys
import json
import subprocess
from pathlib import Path
from typing import Dict, List, Optional

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import logger, ANIMATION_DRAMA_DIR, ANIMATION_EPISODES_DIR

class AnimationDramaValidator:
    """动画剧本生成系统验证器"""
    
    def __init__(self):
        self.project_name = "save_witch_whole"
        self.base_dir = Path(ANIMATION_DRAMA_DIR)
        self.episodes_dir = Path(ANIMATION_EPISODES_DIR) / self.project_name
        self.raw_text_dir = self.base_dir / "raw_text"
        
        self.issues = []
        self.recommendations = []
    
    def log_issue(self, category: str, message: str, severity: str = "warning"):
        """记录问题"""
        self.issues.append({
            "category": category,
            "message": message,
            "severity": severity
        })
        if severity == "error":
            logger.error(f"[{category}] {message}")
        else:
            logger.warning(f"[{category}] {message}")
    
    def log_recommendation(self, message: str):
        """记录建议"""
        self.recommendations.append(message)
        logger.info(f"[建议] {message}")
    
    def check_environment(self) -> bool:
        """检查环境配置"""
        logger.info("检查环境配置...")
        
        # 检查必要的环境变量
        required_env_vars = [
            "AZURE_SUBSCRIPTION_KEY",
            "AZURE_REGION"
        ]
        
        for var in required_env_vars:
            if not os.getenv(var):
                self.log_issue("环境配置", f"缺少环境变量: {var}", "error")
        
        # 检查Python包
        required_packages = [
            "azure.cognitiveservices.speech",
            "pydub",
            "PIL",
            "requests"
        ]
        
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                self.log_issue("环境配置", f"缺少Python包: {package}", "error")
        
        # 检查FFmpeg
        try:
            result = subprocess.run(["ffmpeg", "-version"], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                self.log_issue("环境配置", "FFmpeg不可用", "error")
        except FileNotFoundError:
            self.log_issue("环境配置", "FFmpeg未安装", "error")
        
        return len([i for i in self.issues if i["severity"] == "error"]) == 0
    
    def check_existing_files(self) -> Dict:
        """检查现有文件状态"""
        logger.info("检查现有文件状态...")
        
        file_status = {
            "novel_file": self.raw_text_dir / f"{self.project_name}.txt",
            "summary_file": self.raw_text_dir / f"{self.project_name}.json",
            "group_file": self.raw_text_dir / f"{self.project_name}_group.json",
            "outline_file": self.raw_text_dir / f"{self.project_name}_story_outline.json",
            "episodes_dir": self.episodes_dir
        }
        
        status = {}
        for name, path in file_status.items():
            exists = path.exists()
            status[name] = {
                "path": str(path),
                "exists": exists,
                "size": path.stat().st_size if exists and path.is_file() else 0
            }
            
            if not exists:
                if name == "novel_file":
                    self.log_issue("文件检查", f"原始小说文件不存在: {path}", "error")
                else:
                    self.log_issue("文件检查", f"文件不存在: {path}")
        
        # 检查剧集文件
        if self.episodes_dir.exists():
            episode_files = list(self.episodes_dir.glob("episode_*.json"))
            episode_files = [f for f in episode_files if "_timing" not in f.name]
            
            status["episodes"] = []
            for episode_file in sorted(episode_files):
                episode_name = episode_file.stem
                episode_status = {
                    "name": episode_name,
                    "script_file": str(episode_file),
                    "audio_file": str(episode_file.parent / f"{episode_name}.wav"),
                    "timing_file": str(episode_file.parent / f"{episode_name}_timing.json"),
                    "video_file": str(episode_file.parent / f"{episode_name}.mp4"),
                    "script_exists": episode_file.exists(),
                    "audio_exists": (episode_file.parent / f"{episode_name}.wav").exists(),
                    "timing_exists": (episode_file.parent / f"{episode_name}_timing.json").exists(),
                    "video_exists": (episode_file.parent / f"{episode_name}.mp4").exists()
                }
                status["episodes"].append(episode_status)
        else:
            status["episodes"] = []
        
        return status
    
    def validate_json_structure(self, file_path: Path, expected_structure: Dict) -> bool:
        """验证JSON文件结构"""
        if not file_path.exists():
            return False
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 简单的结构验证
            for key, value_type in expected_structure.items():
                if key not in data:
                    self.log_issue("数据结构", f"{file_path.name} 缺少字段: {key}")
                    return False
                
                if not isinstance(data[key], value_type):
                    self.log_issue("数据结构", f"{file_path.name} 字段类型错误: {key}")
                    return False
            
            return True
        except json.JSONDecodeError as e:
            self.log_issue("数据结构", f"{file_path.name} JSON格式错误: {e}", "error")
            return False
    
    def check_data_consistency(self, file_status: Dict) -> bool:
        """检查数据一致性"""
        logger.info("检查数据一致性...")
        
        # 验证摘要文件结构
        summary_file = Path(file_status["summary_file"]["path"])
        if summary_file.exists():
            expected_summary_structure = {
                # 假设是章节摘要列表
            }
            # 这里可以添加更详细的结构验证
        
        # 验证剧集文件结构
        for episode in file_status.get("episodes", []):
            episode_file = Path(episode["script_file"])
            if episode_file.exists():
                expected_episode_structure = {
                    "ep": dict,
                    "ep_n": int
                }
                self.validate_json_structure(episode_file, expected_episode_structure)
        
        return True
    
    def check_audio_generation_readiness(self, file_status: Dict) -> bool:
        """检查音频生成准备情况"""
        logger.info("检查音频生成准备情况...")
        
        # 检查语音配置文件
        voice_config_files = [
            "voice_config.json",
            "voice_config_example.json"
        ]
        
        voice_config_exists = False
        for config_file in voice_config_files:
            if Path(config_file).exists():
                voice_config_exists = True
                break
        
        if not voice_config_exists:
            self.log_issue("音频生成", "缺少语音配置文件", "error")
            self.log_recommendation("创建 voice_config.json 文件，参考 voice_config_example.json")
        
        # 检查剧集文件是否包含对话和旁白
        for episode in file_status.get("episodes", []):
            if episode["script_exists"]:
                episode_file = Path(episode["script_file"])
                try:
                    with open(episode_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # 检查是否有场景和对话
                    if "ep" in data and "scenes" in data["ep"]:
                        scenes = data["ep"]["scenes"]
                        if not scenes:
                            self.log_issue("音频生成", f"{episode['name']} 没有场景数据")
                    else:
                        self.log_issue("音频生成", f"{episode['name']} 结构不完整")
                        
                except Exception as e:
                    self.log_issue("音频生成", f"无法读取 {episode['name']}: {e}")
        
        return voice_config_exists
    
    def generate_optimization_report(self) -> Dict:
        """生成优化报告"""
        logger.info("生成优化报告...")
        
        report = {
            "summary": {
                "total_issues": len(self.issues),
                "error_count": len([i for i in self.issues if i["severity"] == "error"]),
                "warning_count": len([i for i in self.issues if i["severity"] == "warning"]),
                "recommendations_count": len(self.recommendations)
            },
            "issues": self.issues,
            "recommendations": self.recommendations,
            "next_steps": []
        }
        
        # 生成下一步建议
        if report["summary"]["error_count"] > 0:
            report["next_steps"].append("修复所有错误级别的问题")
        
        if not any("voice_config.json" in str(Path(r)) for r in self.recommendations):
            report["next_steps"].append("配置语音文件")
        
        report["next_steps"].append("运行完整的生成流程测试")
        
        return report
    
    def run_full_validation(self) -> Dict:
        """运行完整验证"""
        logger.info("开始完整验证...")
        
        # 1. 检查环境
        env_ok = self.check_environment()
        
        # 2. 检查文件状态
        file_status = self.check_existing_files()
        
        # 3. 检查数据一致性
        data_ok = self.check_data_consistency(file_status)
        
        # 4. 检查音频生成准备情况
        audio_ready = self.check_audio_generation_readiness(file_status)
        
        # 5. 生成报告
        report = self.generate_optimization_report()
        report["file_status"] = file_status
        report["validation_results"] = {
            "environment_ok": env_ok,
            "data_consistent": data_ok,
            "audio_ready": audio_ready
        }
        
        return report


def main():
    """主函数"""
    validator = AnimationDramaValidator()
    report = validator.run_full_validation()
    
    # 输出报告
    print("\n" + "="*60)
    print("动画剧本生成系统验证报告")
    print("="*60)
    
    print(f"\n📊 总体状况:")
    print(f"  错误: {report['summary']['error_count']}")
    print(f"  警告: {report['summary']['warning_count']}")
    print(f"  建议: {report['summary']['recommendations_count']}")
    
    if report["summary"]["error_count"] > 0:
        print(f"\n❌ 发现的错误:")
        for issue in report["issues"]:
            if issue["severity"] == "error":
                print(f"  - [{issue['category']}] {issue['message']}")
    
    if report["summary"]["warning_count"] > 0:
        print(f"\n⚠️  警告:")
        for issue in report["issues"]:
            if issue["severity"] == "warning":
                print(f"  - [{issue['category']}] {issue['message']}")
    
    if report["recommendations"]:
        print(f"\n💡 建议:")
        for rec in report["recommendations"]:
            print(f"  - {rec}")
    
    print(f"\n📋 下一步:")
    for step in report["next_steps"]:
        print(f"  - {step}")
    
    # 显示文件状态
    print(f"\n📁 文件状态:")
    episodes = report["file_status"].get("episodes", [])
    if episodes:
        print(f"  找到 {len(episodes)} 个剧集:")
        for ep in episodes:
            status_icons = []
            status_icons.append("📝" if ep["script_exists"] else "❌")
            status_icons.append("🎵" if ep["audio_exists"] else "❌")
            status_icons.append("⏰" if ep["timing_exists"] else "❌")
            status_icons.append("🎬" if ep["video_exists"] else "❌")
            print(f"    {ep['name']}: {' '.join(status_icons)}")
    else:
        print("  没有找到剧集文件")
    
    # 保存详细报告
    report_file = "validation_report.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    print(f"\n📄 详细报告已保存至: {report_file}")
    
    # 返回状态码
    if report["summary"]["error_count"] > 0:
        print(f"\n❌ 验证失败，请修复错误后重试")
        return 1
    else:
        print(f"\n✅ 验证通过，系统准备就绪")
        return 0


if __name__ == "__main__":
    sys.exit(main())
