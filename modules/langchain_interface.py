# 更新导入路径
from langchain_openai import AzureChatOpenAI, ChatOpenAI
from langchain_anthropic import ChatAnthropic
from langchain_google_genai import ChatGoogleGenerativeAI  # 添加 Google Generative AI 支持
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import StrOutputParser
# 添加LangChain链式支持
from langchain.chains import LL<PERSON>hain, SequentialChain, SimpleSequentialChain
from langchain.memory import SimpleMemory
# 使用 OpenAI 兼容接口调用 DeepSeek
from openai import OpenAI, AzureOpenAI

from typing import Dict, Any, Optional, List, Union, Tuple
import os
from pathlib import Path
import json
import time
import logging
import re
import json5
import hashlib
from datetime import datetime, timezone, timedelta
import threading
from dotenv import load_dotenv
from modules.utils import calculate_token_count

import sys
import torch
import numpy as np
from FlagEmbedding import BGEM3FlagModel


from modules.gpt_parameters import (
    get_gpt_parameters, 
    get_model_param_compatibility,
    PROMPT_COLLECTIONS,
    # 提供商常量
    LLM_PROVIDER_AZURE,
    LLM_PROVIDER_OPENAI,
    LLM_PROVIDER_ANTHROPIC,
    LLM_PROVIDER_DEEPSEEK,
    LLM_PROVIDER_LOCAL_DEEPSEEK,  # 添加本地 DeepSeek 提供商
    LLM_PROVIDER_GOOGLE,  # Google 提供商常量
    # 模型键名常量
    MODEL_KEY_GPT41,  # 添加 GPT-4.1 模型键名常量
    MODEL_KEY_O3_MINI,
    MODEL_KEY_CLAUDE_OPUS,
    MODEL_KEY_CLAUDE_SONNET,
    MODEL_KEY_CLAUDE_HAIKU,
    MODEL_KEY_DEEPSEEK_CHAT,
    MODEL_KEY_DEEPSEEK_REASONER,
    MODEL_KEY_ARK_DEEPSEEK_R1,
    MODEL_KEY_LOCAL_DEEPSEEK_R1,  # 添加本地 DeepSeek 模型键名
    MODEL_KEY_GEMINI_25_PRO,  # Gemini 2.5 Pro 模型键名常量
    # 环境变量名称
    ENV_AZURE_OPENAI_ENDPOINT,
    ENV_AZURE_OPENAI_API_KEY,
    ENV_OPENAI_API_KEY,
    ENV_ANTHROPIC_API_KEY,
    ENV_DEEPSEEK_API_KEY,
    ENV_DEEPSEEK_API_BASE,
    ENV_DEEPSEEK_ARK_API_KEY,
    ENV_LOCAL_DEEPSEEK_API_BASE,  # 添加本地 DeepSeek API 基础 URL 环境变量
    ENV_GEMINI_API_KEY,  # Gemini API 密钥环境变量名称
    ENV_CACHE_DIR,
    ENV_CACHE_EXPIRY_DAYS,
    # 默认值
    DEFAULT_AZURE_ENDPOINT,
    DEFAULT_DEEPSEEK_API_BASE,
    DEFAULT_LOCAL_DEEPSEEK_API_BASE,  # 添加本地 DeepSeek API 基础 URL 默认值
    DEFAULT_CACHE_DIR,
    DEFAULT_CACHE_EXPIRY_DAYS,
    DEEPSEEK_ARK_API_BASE,
    # 配置
    MODEL_CONFIGS,
    MODEL_PARAM_COMPATIBILITY,
    load_model_settings,
    DEFAULT_LLM_TYPE,
    DEFAULT_MODEL_KEY,
    get_prompt_and_system_message
)

# 加载环境变量
load_dotenv()

# 设置日志记录
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# 获取 API 配置 - 使用统一的环境变量名称
AZURE_OPENAI_ENDPOINT = os.getenv(ENV_AZURE_OPENAI_ENDPOINT, DEFAULT_AZURE_ENDPOINT)
AZURE_OPENAI_API_KEY = os.getenv(ENV_AZURE_OPENAI_API_KEY)
OPENAI_API_KEY = os.getenv(ENV_OPENAI_API_KEY)
ANTHROPIC_API_KEY = os.getenv(ENV_ANTHROPIC_API_KEY)
DEEPSEEK_API_KEY = os.getenv(ENV_DEEPSEEK_API_KEY)
DEEPSEEK_API_BASE = os.getenv(ENV_DEEPSEEK_API_BASE, DEFAULT_DEEPSEEK_API_BASE)
DEEPSEEK_ARK_API_KEY = os.getenv(ENV_DEEPSEEK_ARK_API_KEY)
LOCAL_DEEPSEEK_API_BASE = os.getenv(ENV_LOCAL_DEEPSEEK_API_BASE, DEFAULT_LOCAL_DEEPSEEK_API_BASE)  # 获取本地 DeepSeek API 基础 URL
GEMINI_API_KEY = os.getenv(ENV_GEMINI_API_KEY)  # 获取 Gemini API 密钥

# 导出模型名称常量，保持向后兼容
OPENAI_GPT41_MODEL = MODEL_KEY_GPT41  # 添加 GPT-4.1 模型常量
OPENAI_o3_mini_MODEL = MODEL_KEY_O3_MINI
CLAUDE_3_OPUS_MODEL = MODEL_KEY_CLAUDE_OPUS
CLAUDE_3_SONNET_MODEL = MODEL_KEY_CLAUDE_SONNET
CLAUDE_3_HAIKU_MODEL = MODEL_KEY_CLAUDE_HAIKU
DEEPSEEK_REASONER_MODEL = MODEL_KEY_DEEPSEEK_REASONER
DEEPSEEK_CHAT_MODEL = MODEL_KEY_DEEPSEEK_CHAT

# 缓存配置
CACHE_DIR = Path(os.getenv(ENV_CACHE_DIR, DEFAULT_CACHE_DIR))
CACHE_EXPIRY_DAYS = int(os.getenv(ENV_CACHE_EXPIRY_DAYS, str(DEFAULT_CACHE_EXPIRY_DAYS)))
CACHE_DIR.mkdir(parents=True, exist_ok=True)
cache_lock = threading.Lock()

# 自己实现缓存相关函数，不再依赖 gpt4_interface.py
def get_cache_key(api_function: str, prompt_data: Dict[str, Any]) -> str:
    """生成缓存键"""
    # 创建一个可排序的提示数据副本
    sorted_prompt_data = {}
    for key in sorted(prompt_data.keys()):
        sorted_prompt_data[key] = prompt_data[key]
    
    # 组合API函数和提示数据
    combined_data = f"{api_function}:{json.dumps(sorted_prompt_data, sort_keys=True)}"
    
    # 生成哈希值
    return hashlib.md5(combined_data.encode('utf-8')).hexdigest()

def get_cached_response(cache_key: str) -> Optional[str]:
    """从缓存获取响应"""
    cache_file = CACHE_DIR / f"{cache_key}.json"
    
    if not cache_file.exists():
        return None
        
    # 检查缓存是否过期
    file_age = datetime.now(timezone.utc) - datetime.fromtimestamp(
        cache_file.stat().st_mtime, tz=timezone.utc
    )
    if file_age > timedelta(days=CACHE_EXPIRY_DAYS):
        return None
        
    try:
        with cache_file.open('r', encoding='utf-8') as f:
            cached_data = json.load(f)
            return cached_data.get('response')
    except (json.JSONDecodeError, IOError) as e:
        logger.warning(f"读取缓存失败: {str(e)}")
        return None

def save_to_cache(cache_key: str, response: str, require_json: bool = False) -> None:
    """保存响应到缓存"""
    if not response:
        return
        
    if require_json:
        try:
            # 验证响应是否为有效的JSON
            if isinstance(response, str):
                json.loads(response)
        except json.JSONDecodeError:
            logger.warning(f"无法缓存非JSON响应")
            return
            
    cache_file = CACHE_DIR / f"{cache_key}.json"
    
    cache_data = {
        'response': response,
        'timestamp': datetime.now(timezone.utc).isoformat()
    }
    
    with cache_lock:
        try:
            with cache_file.open('w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
        except IOError as e:
            logger.warning(f"保存缓存失败: {str(e)}")

# 验证函数
def validate_gpt_response(response: Union[str, Dict[str, Any]], expected_fields: Optional[List[str]] = None) -> Tuple[bool, List[str], str]:
    """验证GPT响应"""
    if response is None:
        return False, [], "响应为空"
        
    # 如果响应是字符串且需要验证字段，尝试解析为JSON
    if isinstance(response, str) and expected_fields:
        try:
            response = json.loads(response)
        except json.JSONDecodeError:
            return False, [], "响应不是有效的JSON"
            
    # 验证字段
    if expected_fields and isinstance(response, dict):
        missing_fields = []
        for field in expected_fields:
            parts = field.split('.')
            current = response
            for part in parts:
                if not isinstance(current, dict) or part not in current:
                    missing_fields.append(field)
                    break
                current = current[part]
                
        if missing_fields:
            return False, missing_fields, f"缺少字段: {', '.join(missing_fields)}"
            
    return True, [], ""

# 错误类
class GPTResponseError(Exception):
    """GPT响应错误"""
    pass


# 改进 extract_json_from_text 函数，更好地处理 Markdown 代码块
def extract_json_from_text(text: str) -> str:
    """从文本中提取 JSON 内容，处理多种非标准格式情况
    
    Args:
        text: 原始文本响应
        
    Returns:
        提取后的 JSON 文本
    """
    if not text:
        return ""
        
    # 检查是否包含 markdown 代码块
    json_block_pattern = r'```(?:json)?\s*([\s\S]*?)```'
    matches = re.findall(json_block_pattern, text)
    
    if matches:
        # 使用第一个匹配的代码块内容
        cleaned_text = matches[0].strip()
    else:
        # 如果没有代码块，直接清理文本
        cleaned_text = text.strip()
        # 移除可能的单独的代码块标记
        cleaned_text = re.sub(r'```(?:json)?|```', '', cleaned_text)
    
    # 查找第一个有效的 JSON 开始位置
    json_start = -1
    for char in ['{', '[']:
        pos = cleaned_text.find(char)
        if pos != -1 and (json_start == -1 or pos < json_start):
            json_start = pos
    
    if json_start == -1:
        return cleaned_text.strip()  # 没找到 JSON 开始标记
    
    # 查找对应的 JSON 结束位置
    if cleaned_text[json_start] == '{':
        # 处理对象
        brackets_count = 0
        for i in range(json_start, len(cleaned_text)):
            if cleaned_text[i] == '{':
                brackets_count += 1
            elif cleaned_text[i] == '}':
                brackets_count -= 1
                if brackets_count == 0:
                    return cleaned_text[json_start:i+1].strip()
    else:
        # 处理数组
        brackets_count = 0
        for i in range(json_start, len(cleaned_text)):
            if cleaned_text[i] == '[':
                brackets_count += 1
            elif cleaned_text[i] == ']':
                brackets_count -= 1
                if brackets_count == 0:
                    return cleaned_text[json_start:i+1].strip()
    
    # 找不到匹配的结束括号，返回从开始位置到结尾
    return cleaned_text[json_start:].strip()

# 使用 LangChain 调用模型
def call_model_with_langchain(
    prompt: str,
    system_message: str,
    llm_type: str = LLM_PROVIDER_AZURE,
    model_key: str = MODEL_KEY_GPT41,
    temperature: float = None,
    top_p: float = None,
    max_tokens: Optional[int] = None,
    timeout: Optional[int] = 600  # 10分钟超时，适合大型请求
) -> str:
    """使用 LangChain 调用大语言模型"""
    try:
        # 获取模型参数兼容性配置
        compatibility = get_model_param_compatibility(llm_type, model_key)
        
        # 获取模型配置
        if llm_type in MODEL_CONFIGS and model_key in MODEL_CONFIGS[llm_type]:
            config = MODEL_CONFIGS[llm_type][model_key]
        else:
            # 如果没有找到配置，使用默认值
            config = {}
            logger.warning(f"未找到模型配置的参数: {llm_type}/{model_key}，使用默认值")
          
        # 根据 LLM 类型选择不同的调用方式
        if llm_type == LLM_PROVIDER_AZURE:
            # 客户端参数
            client_params = {
                "azure_endpoint": AZURE_OPENAI_ENDPOINT,
                "api_key": AZURE_OPENAI_API_KEY,
                "api_version": config.get("api_version", "2024-12-01-preview"),
                "deployment_name": config.get("deployment_name", model_key),
                "request_timeout": timeout  # 正确的参数名称是 request_timeout
            }
            
            # 为 o3-mini 模型特殊处理
            if model_key == MODEL_KEY_O3_MINI:   
                # 使用 model_kwargs 传递 max_completion_tokens
                max_completion_tokens = config.get("default_max_tokens", 100000)
                client_params["model_kwargs"] = {
                    "max_completion_tokens": max_completion_tokens
                }
            else:
                # 其他模型使用标准参数
                max_token_value = max_tokens if max_tokens is not None else config.get("default_max_tokens", 100000)
                
                # 添加安全检查，确保不超过模型支持的最大值
                if model_key == MODEL_KEY_GPT41 and max_token_value > 32768:
                    logger.warning(f"GPT-4.1 max_tokens超过限制，自动调整为32000")
                    max_token_value = 32000
                
                client_params["max_tokens"] = max_token_value
            
            # 请求参数
            if compatibility.get("supports_temperature", True):
                client_params["temperature"] = temperature
                
            if compatibility.get("supports_top_p", True):
                client_params["top_p"] = top_p
            
            logger.debug(f"Azure API 请求参数: {client_params}")
            # 创建客户端
            client = AzureChatOpenAI(**client_params)
            
            # 使用 invoke 方法而不是 generate
            messages = [
                SystemMessage(content=system_message),
                HumanMessage(content=prompt)
            ]
            response = client.invoke(messages)
            return response.content
            
        elif llm_type == LLM_PROVIDER_DEEPSEEK:
            # DeepSeek OpenAI 兼容接口
            try:
                # 根据模型键名确定是否使用 ARK API
                use_ark_api = model_key == MODEL_KEY_ARK_DEEPSEEK_R1
                
                # 根据模型选择正确的 API 基础 URL 和密钥
                if use_ark_api:
                    base_url = config.get("base_url", DEEPSEEK_ARK_API_BASE)
                    api_key = DEEPSEEK_ARK_API_KEY
                    logger.info(f"使用 DeepSeek ARK API: {base_url}, 模型: {model_key}")
                else:
                    base_url = config.get("base_url", DEEPSEEK_API_BASE)
                    api_key = DEEPSEEK_API_KEY
                    logger.info(f"使用标准 DeepSeek API: {base_url}, 模型: {model_key}")
                
                # 确保 API 密钥可用
                if not api_key:
                    error_msg = "DeepSeek API 密钥未设置" if not use_ark_api else "DeepSeek ARK API 密钥未设置"
                    logger.error(error_msg)
                    raise ValueError(error_msg)
                
                # 创建 OpenAI 客户端
                client = OpenAI(
                    base_url=base_url,
                    api_key=api_key
                )
                
                # 构建请求参数 - 使用正确的模型名称
                request_params = {
                    "model": model_key,  # 直接使用model_key
                    "temperature": temperature,
                    "top_p": top_p
                }
                
                # 只有显式传递max_tokens时添加该参数
                if max_tokens is not None:
                    request_params["max_tokens"] = max_tokens
                
                logger.debug(f"DeepSeek API 请求参数: {request_params}")
                
                # 调用API
                response = client.chat.completions.create(
                    messages=[
                        {"role": "system", "content": system_message},
                        {"role": "user", "content": prompt}
                    ],
                    **request_params
                )
                
                #logger.debug(f"DeepSeek API 响应状态: 成功")
                return response.choices[0].message.content
                
            except Exception as e:
                error_msg = str(e)
                logger.error(f"DeepSeek API 调用失败: {error_msg}")
                logger.error(f"请求参数: model={model_key}, params={request_params}")
                raise
        
        elif llm_type == LLM_PROVIDER_LOCAL_DEEPSEEK:
            # 本地 DeepSeek 模型处理逻辑
            try:
                base_url = config.get("base_url", LOCAL_DEEPSEEK_API_BASE)
                logger.info(f"使用本地 DeepSeek API: {base_url}, 模型: {model_key}")
                
                # 本地 Ollama API 不需要 API 密钥
                client = OpenAI(
                    base_url=base_url,
                    api_key="EMPTY"  # Ollama 不需要 API 密钥，但 OpenAI 客户端要求提供
                )
                
                # 构建请求参数
                request_params = {
                    "model": model_key,  # 使用模型名称，例如 "huihui_ai/deepseek-r1-abliterated:32b"
                }
                
                # 根据兼容性添加参数
                if compatibility.get("supports_temperature", True):
                    request_params["temperature"] = temperature
                
                if compatibility.get("supports_top_p", True):
                    request_params["top_p"] = top_p
                
                # 只有显式传递max_tokens时添加该参数
                if max_tokens is not None:
                    request_params["max_tokens"] = max_tokens
                
                logger.debug(f"本地 DeepSeek API 请求参数: {request_params}")
                
                # 调用API
                response = client.chat.completions.create(
                    messages=[
                        {"role": "system", "content": system_message},
                        {"role": "user", "content": prompt}
                    ],
                    **request_params
                )
                
                logger.debug(f"本地 DeepSeek API 响应状态: 成功")
                return response.choices[0].message.content
                
            except Exception as e:
                error_msg = str(e)
                logger.error(f"本地 DeepSeek API 调用失败: {error_msg}")
                logger.error(f"请求参数: model={model_key}, params={request_params}")
                raise
        
        elif llm_type == LLM_PROVIDER_GOOGLE:
            # Google Gemini 模型处理逻辑
            try:
                # 检查 API 密钥可用性
                if not GEMINI_API_KEY:
                    error_msg = "Google Gemini API 密钥未设置"
                    logger.error(error_msg)
                    raise ValueError(error_msg)
                
                # 使用 LangChain 的 Google Generative AI 集成
                client_params = {
                    "model": model_key,  # "gemini-2.5-pro"
                    "google_api_key": GEMINI_API_KEY,
                    "request_timeout": timeout
                }
                
                # 根据兼容性配置添加参数
                if compatibility.get("supports_temperature", True):
                    client_params["temperature"] = temperature
                
                if compatibility.get("supports_top_p", True):
                    client_params["top_p"] = top_p
                
                # Google Gemini 使用 max_output_tokens 而不是 max_tokens
                if max_tokens is not None:
                    client_params["max_output_tokens"] = max_tokens
                else:
                    # 使用默认值
                    default_max_tokens = config.get("default_max_tokens")
                    if default_max_tokens:
                        client_params["max_output_tokens"] = default_max_tokens
                
                logger.debug(f"Google Gemini API 请求参数: {client_params}")

                # 创建客户端
                client = ChatGoogleGenerativeAI(**client_params)
                
                # 使用 invoke 方法
                messages = [
                    SystemMessage(content=system_message),
                    HumanMessage(content=prompt)
                ]
                response = client.invoke(messages)
                return response.content
                
            except Exception as e:
                error_msg = str(e)
                logger.error(f"Google Gemini API 调用失败: {error_msg}")
                raise
        
        # 其他模型类型保持不变...
        else:
            raise ValueError(f"不支持的 LLM 类型: {llm_type}")
        
    except Exception as e:
        # 增强错误日志，包含更多调试信息
        logger.error(f"调用 LangChain 模型失败: {str(e)}, 模型类型: {llm_type}, 模型: {model_key}")
        # 重新抛出异常以便上层函数处理
        raise

# 添加获取当前模型配置的函数
def get_current_model_config() -> Tuple[str, str]:
    """
    获取当前应使用的LLM类型和模型键名
    
    优先级：
    1. 环境变量
    2. 配置文件
    3. 默认值
    
    Returns:
        Tuple[str, str]: (llm_type, model_key) 元组
    """
    # 首先检查环境变量
    env_llm_type = os.getenv("LLM_TYPE")
    env_model_key = os.getenv("MODEL_KEY")
    
    if env_llm_type and env_model_key:
        # 验证环境变量配置是否有效
        valid_llm_types = [
            LLM_PROVIDER_AZURE,
            LLM_PROVIDER_OPENAI,
            LLM_PROVIDER_ANTHROPIC,
            LLM_PROVIDER_DEEPSEEK,
            LLM_PROVIDER_LOCAL_DEEPSEEK,
            LLM_PROVIDER_GOOGLE  # 添加 Google 提供商
        ]
        
        if env_llm_type not in valid_llm_types:
            logger.warning(f"环境变量中的 LLM 类型 {env_llm_type} 无效，使用默认值")
        else:
            # 验证模型键名与提供商兼容性
            if env_llm_type == LLM_PROVIDER_GOOGLE and env_model_key != MODEL_KEY_GEMINI_25_PRO:
                logger.warning(f"Google 提供商只支持 {MODEL_KEY_GEMINI_25_PRO} 模型，自动调整")
                env_model_key = MODEL_KEY_GEMINI_25_PRO
            
            return env_llm_type, env_model_key
    
    # 使用预加载的默认值（来自配置文件或默认设置）
    return DEFAULT_LLM_TYPE, DEFAULT_MODEL_KEY

# 修改 call_llm 函数，确保使用API函数定义的参数
def call_llm(
    api_function: str,
    prompt_data: Dict[str, Any],
    llm_type: str = None,
    model_key: str = None,
    using_cache: bool = True,
    temperature: Optional[float] = None,
    top_p: Optional[float] = None,
    max_tokens: Optional[int] = None
) -> str:
    """调用大语言模型"""
    # 如果未指定模型配置，使用当前配置
    if llm_type is None or model_key is None:
        current_llm_type, current_model_key = get_current_model_config()
        llm_type = llm_type or current_llm_type
        model_key = model_key or current_model_key
       
    try:
         # 生成缓存键
        cache_key = get_cache_key(api_function, prompt_data)
    
        # 检查缓存
        if using_cache:
            cached_response = get_cached_response(cache_key)
            if cached_response:
                logger.info(f"使用缓存的响应: {api_function}")
                return cached_response
        
        # 获取提示词和系统消息
        prompt_template, system_message = get_prompt_and_system_message(api_function)

        # 格式化提示词
        prompt = prompt_template.format(**prompt_data)

        # 获取API函数的参数配置
        api_params = get_gpt_parameters(api_function, llm_type, model_key)

        # 获取默认参数配置
        default_params = get_gpt_parameters("default", llm_type, model_key)

        token_count = calculate_token_count(prompt)
        logger.info(f"Calling {llm_type}/{model_key} model for {api_function}, prompt tokens: {token_count}")

        # 参数优先级: 显式传递的参数 > API函数定义的参数 > 默认参数配置
        final_temperature = temperature if temperature is not None else api_params.get("temperature", default_params.get("temperature"))
        final_top_p = top_p if top_p is not None else api_params.get("top_p", default_params.get("top_p"))

        # 获取模型配置中的默认 max_tokens
        model_config = MODEL_CONFIGS.get(llm_type, {}).get(model_key, {})
        default_max_tokens = model_config.get("default_max_tokens")
        final_max_tokens = max_tokens if max_tokens is not None else api_params.get("max_tokens", default_max_tokens)

        # 调用模型
        response = call_model_with_langchain(
            prompt=prompt,
            system_message=system_message,
            llm_type=llm_type,
            model_key=model_key,
            temperature=final_temperature,
            top_p=final_top_p,
            max_tokens=final_max_tokens
        )

        # 保存到缓存
        if using_cache and response:
            save_to_cache(cache_key, response)

        return response
    except Exception as e:
        logger.error(f"Error occurred within call_llm for api_function '{api_function}': {str(e)}", exc_info=False)
        raise

# 修改 call_llm_json_response 函数，确保在模型切换后重新获取参数
def call_llm_json_response(
    api_function: str,
    prompt_data: Dict[str, Any],
    max_retries: int = 5,
    using_cache: bool = True,
    llm_type: str = None,
    model_key: str = None,
    expected_fields: Optional[List[str]] = None,
    max_tokens: Optional[int] = None
) -> Optional[Dict[str, Any]]:
    """调用LLM并返回JSON响应"""
    # 初始化模型配置
    current_llm_type, current_model_key = get_current_model_config()
    llm_type = llm_type or current_llm_type
    model_key = model_key or current_model_key
    
    # 定义备用模型配置
    FALLBACK_CONFIG = {
        LLM_PROVIDER_DEEPSEEK: MODEL_KEY_ARK_DEEPSEEK_R1
    }

    retry_count = 0
    last_error = None
    cache_key = get_cache_key(api_function, prompt_data)

    while retry_count < max_retries:
        try:
            # 获取API函数的参数配置 - 确保每次重试都重新获取参数
            api_params = get_gpt_parameters(api_function, llm_type, model_key)
            
            # 只在第一次尝试时检查缓存
            current_using_cache = using_cache and retry_count == 0
            
            # 调用 LLM，使用API特定的参数
            response_text = call_llm(
                api_function=api_function,
                prompt_data=prompt_data,
                llm_type=llm_type,
                model_key=model_key,
                using_cache=current_using_cache,
                temperature=api_params.get("temperature"),  # 不提供默认值，让call_llm使用API函数定义的参数
                top_p=api_params.get("top_p"),
                max_tokens=max_tokens if max_tokens is not None else api_params.get("max_tokens")
            )
            
            if response_text:
                try:
                    # 提取 JSON 部分
                    json_text = extract_json_from_text(response_text)
                    #logger.debug(f"提取的 JSON 文本: {json_text}...")
                    
                    # 直接使用 json5 进行解析（更宽容的解析器）
                    try:
                        json_response = json5.loads(json_text)
                    except Exception as json_err:
                        # 记录完整的错误信息和原始响应
                        logger.error(f"JSON 解析失败: {str(json_err)}")
                        #logger.error(f"原始响应: {response_text}")
                        logger.error(f"提取的 JSON: {json_text}")
                        raise
                    
                    # 验证期望的字段
                    if expected_fields:
                        missing_fields = []
                        for field in expected_fields:
                            parts = field.split('.')
                            current = json_response
                            for part in parts:
                                if not isinstance(current, dict) or part not in current:
                                    missing_fields.append(field)
                                    break
                                current = current[part]
                        
                        if missing_fields:
                            logger.warning(f"JSON 响应缺少字段: {missing_fields}")
                            retry_count += 1
                            continue
                    
                    # 保存缓存前验证 JSON 有效性
                    if using_cache:
                        try:
                            # 再次验证 JSON 有效性
                            test_json = json5.loads(json_text)
                            save_to_cache(cache_key, json_text, require_json=True)
                        except Exception as cache_err:
                            logger.warning(f"无效的 JSON 不会被缓存: {str(cache_err)}")
                    
                    return json_response
                    
                except Exception as e:
                    last_error = str(e)
                    logger.warning(f"第 {retry_count + 1} 次尝试出错: {last_error}")
            else:
                last_error = "空响应"
                logger.warning(f"第 {retry_count + 1} 次尝试出错: {last_error}")
                
        except Exception as e:
            error_msg = str(e)
            last_error = error_msg
            
            # 检测到内容过滤错误时切换模型
            if "content filter" in error_msg.lower() or "content_filter" in error_msg.lower():
                # logger.warning("检测到内容过滤限制，尝试切换备用模型")
                new_llm_type = LLM_PROVIDER_DEEPSEEK
                if new_llm_type in FALLBACK_CONFIG:
                    llm_type = new_llm_type
                    model_key = FALLBACK_CONFIG[new_llm_type]
                    logger.info(f"检测到内容过滤限制，切换模型配置到: {llm_type}/{model_key}")
                    retry_count -= 1  # 重置重试计数器
            if retry_count >= 0:
                logger.warning(f"第 {retry_count + 1} 次尝试出错: {last_error}")
            
        retry_count += 1
        if retry_count < max_retries:
            time.sleep(1)  # 添加重试延迟
            continue
    
    # 所有重试都失败后抛出异常
    logger.error(f"调用 LLM JSON 响应失败，达到最大重试次数: {last_error}")
    return None

# 初始化 BGE-M3 模型，全局单例
_bge_model = None
_bge_model_lock = threading.Lock()

def init_bge_model():
    """初始化 BGE-M3 模型（惰性加载）"""
    global _bge_model
    with _bge_model_lock:
        if _bge_model is not None:
            return _bge_model
            
        # 检测平台和可用设备
        if sys.platform == "darwin":
            # macOS 平台
            # 检查是否支持 MPS（要求 PyTorch 构建时启用 MPS，macOS ≥12.3，且为 Apple Silicon）
            if hasattr(torch.backends, "mps") and torch.backends.mps.is_available():
                device = "mps"
            else:
                device = "cpu"
        else:
            # 非 macOS 平台，优先 CUDA
            if torch.cuda.is_available():
                device = "cuda:0"
            else:
                device = "cpu"

        # 是否启用 fp16（仅在 CUDA 上启用以加速计算）
        use_fp16 = device.startswith("cuda")

        logger.info(f"初始化 BGE-M3 嵌入模型，使用设备: {device}, use_fp16: {use_fp16}")

        # 初始化 BGE‑M3 模型
        _bge_model = BGEM3FlagModel(
            "BAAI/bge-m3",
            use_fp16=use_fp16,
            device=device
        )
        
        return _bge_model

def generate_embedding(text: str, using_cache: bool = True) -> np.ndarray:
    """
    为给定文本生成嵌入向量，使用 BGE-M3 模型

    Args:
        text (str): 要生成嵌入向量的文本
        using_cache (bool): 是否使用缓存机制，默认为 True

    Returns:
        np.ndarray: 嵌入向量，NumPy 数组格式
    """
    try:
        # 缓存检查
        if using_cache:
            cache_key = get_cache_key("text-embedding", {"text": text})
            cached_response = get_cached_response(cache_key)
            if cached_response is not None:
                try:
                    # 将缓存的字符串转换回 numpy array
                    return np.array(json.loads(cached_response), dtype=np.float32)
                except Exception as e:
                    logger.warning(f"Failed to load embedding cache: {str(e)}")

        # 计算 token 数量（仅用于日志记录）
        token_count = calculate_token_count(text)
        logger.debug(f"Calling BGE-M3 embedding model, tokens: {token_count}")

        # 惰性加载模型
        model = init_bge_model()
        
        # 生成嵌入向量
        result = model.encode([text], batch_size=1, max_length=8192)
        embedding = np.array(result["dense_vecs"][0], dtype=np.float32)
        
        # 保存到缓存
        if using_cache:
            # 将 numpy array 转换为可序列化的列表
            save_to_cache(cache_key, json.dumps(embedding.tolist()))
            
        return embedding
        
    except Exception as e:
        error_msg = f"Error in generate_embedding: {str(e)}"
        logger.error(error_msg)
        raise

# ============= 图像生成多步链 =============

def create_llm_for_chain(llm_type: str = None, model_key: str = None, temperature: float = 0.7) -> Any:
    """
    为LangChain链创建一个合适的LLM实例
    
    Args:
        llm_type: LLM类型，如Azure、OpenAI等
        model_key: 模型键名，如GPT-4o等
        temperature: 温度参数
        
    Returns:
        用于LangChain链的LLM实例
    """
    # 获取当前的默认模型配置
    if llm_type is None or model_key is None:
        llm_type, model_key = get_current_model_config()
    
    # 获取模型配置
    if llm_type in MODEL_CONFIGS and model_key in MODEL_CONFIGS[llm_type]:
        config = MODEL_CONFIGS[llm_type][model_key]
    else:
        config = {}
        logger.warning(f"未找到模型配置: {llm_type}/{model_key}，使用默认值")
    
    # 根据LLM类型创建合适的LLM实例
    if llm_type == LLM_PROVIDER_AZURE:
        client_params = {
            "azure_endpoint": AZURE_OPENAI_ENDPOINT,
            "api_key": AZURE_OPENAI_API_KEY,
            "api_version": config.get("api_version", "2024-12-01-preview"),
            "deployment_name": config.get("deployment_name", model_key),
            "temperature": temperature
        }
        
        # 为某些模型添加特殊处理
        if model_key == MODEL_KEY_O3_MINI:
            max_completion_tokens = config.get("default_max_tokens", 100000)
            client_params["model_kwargs"] = {
                "max_completion_tokens": max_completion_tokens
            }
        else:
            max_token_value = config.get("default_max_tokens", 100000)
            client_params["max_tokens"] = max_token_value
        
        return AzureChatOpenAI(**client_params)
    
    elif llm_type == LLM_PROVIDER_GOOGLE:
        # 检查 API 密钥可用性
        if not GEMINI_API_KEY:
            logger.error("Google Gemini API 密钥未设置")
            raise ValueError("Google Gemini API 密钥未设置")
        
        # 使用 LangChain 的 Google Generative AI 集成
        client_params = {
            "model": model_key,  # "gemini-2.5-pro"
            "google_api_key": GEMINI_API_KEY,
            "temperature": temperature
        }
        
        # 添加最大生成 tokens 参数
        default_max_tokens = config.get("default_max_tokens", 8192)
        client_params["max_output_tokens"] = default_max_tokens
        
        logger.info(f"为 LangChain 链创建 Google Gemini 模型: {model_key}")
        return ChatGoogleGenerativeAI(**client_params)
    
    # 其他类型LLM可以根据需要扩展
    
    # 默认情况下，警告并返回Azure LLM
    logger.warning(f"未实现的LLM类型: {llm_type}，使用Azure OpenAI代替")
    return AzureChatOpenAI(
        azure_endpoint=AZURE_OPENAI_ENDPOINT,
        api_key=AZURE_OPENAI_API_KEY,
        deployment_name=model_key,
        temperature=temperature
    )
