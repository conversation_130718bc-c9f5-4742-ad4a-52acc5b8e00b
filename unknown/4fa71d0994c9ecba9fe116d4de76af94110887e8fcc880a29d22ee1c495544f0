# modules/utils.py
from modules.nlp_model import get_nlp_model
from config import logger
from typing import Union
import re
import unicodedata
import numpy as np
import os
from langdetect import detect
# 将数据类和验证器移到单独的文件
from dataclasses import dataclass
from typing import List, Dict, Tuple, Any, Callable, Optional
import tiktoken
from functools import wraps
import json
import logging
import datetime
import shutil
import subprocess

from config import DATABASE_PATH, EXPECTED_EMBEDDING_DIMENSION, IMAGE_DIR, SCRIPT_DIR, LANGUAGE_CODES, TOPIC_DIR, FAISS_INDEX_PATH, METADATA_PATH, VECTOR_STORE_DIR, RESIZED_IMAGE_DIR

IMAGE_SIZE_DESCALE_THRESHOLD = 1.5 * 1024  # 降低阈值到1.5MB，解决中等大小图片抖动问题
IMAGE_SIZE_UPSCALE_THRESHOLD = 400  # 新增：图片放大阈值 500K
# 设置日志记录
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

DEFAULT_ENCODING = 'cl100k_base'  # Default tokenizer encoding
UPSCALE_IMAGE_DEBUG = False
# 新增的全局变量
MIN_WORDS = 20  # 每段最小字数
MAX_WORDS = 40  # 每段最大字数

# 新增：默认调整大小阈值 (例如 5MB)
@dataclass
class ChunkSummary:
    plot: str
    characters: str
    scenes: str
    mood: str

@dataclass
class SummaryComponent:
    plot: str
    characters: List[str]
    scenes: List[str]
    key_events: List[str]
    emotions: List[str]

# validators.py
class SummaryValidator:
    @staticmethod
    def validate_summary(summary: Dict[str, Any]) -> bool:
        required_keys = {'plot', 'characters', 'scenes', 'mood'}
        return all(key in summary for key in required_keys)

    @staticmethod
    def validate_merged_summary(summary: Dict[str, Any]) -> bool:
        required_keys = {'plot_thread', 'character_arc', 'dramatic_scenes', 'final_script'}
        return all(key in summary for key in required_keys)

def smart_paragraph_split(
    text: str,
    min_sentences: int = 2,
    max_sentences: int = 5,
    min_words: Optional[int] = None,
    max_words: Optional[int] = None,
    language: str = 'English',
    similarity_threshold: float = 0.5
) -> List[str]:
    """智能分段处理文本"""
    # 如果设置为默认的English，进行语言检测
    if language == 'English':
        try:
            detected_lang = detect(text)
            language = detected_lang
            logger.debug(f"检测到文本语言为: {language}")
        except Exception as e:
            logger.warning(f"语言检测失败: {str(e)}，使用默认语言English")

    # 参数验证
    if min_words and max_words and min_words > max_words:
        raise ValueError("min_words 不能大于 max_words")
    if min_sentences > max_sentences:
        raise ValueError("min_sentences 不能大于 max_sentences")

    # 空文本处理
    if not text.strip():
        return []

    nlp = get_nlp_model(lang=language)
    if not nlp:
        logger.warning("NLP模型加载失败，返回原始文本")
        return [text]

    result_paragraphs = []
    current_paragraph = []
    current_sentences = 0
    current_words = 0

    # 预处理文本，使用现有的 clean_text 函数
    text = clean_text(text)

    # 获取所有句子及其向量
    doc = nlp(text)
    sentences = [sent.text.strip() for sent in doc.sents if sent.text.strip()]
    sentence_vectors = [nlp(sent).vector for sent in sentences]

    for i, sentence in enumerate(sentences):
        sentence_doc = nlp(sentence)
        sentence_words = len(sentence_doc)

        # 处理超长句子 - 直接作为独立段落
        if min_words is not None and max_words is not None and sentence_words > max_words:
            # 先保存当前段落（如果有）
            if current_paragraph:
                paragraph = ''.join(current_paragraph) if language == 'Chinese' else ' '.join(current_paragraph)
                if min_words is None or current_words >= min_words:
                    result_paragraphs.append(paragraph)
                else:
                    # 如果段落太短，尝试合并到前一段
                    if result_paragraphs:
                        last_para = result_paragraphs[-1]
                        combined = (last_para +
                                  (' ' if language != 'Chinese' else '') +
                                  paragraph)
                        result_paragraphs[-1] = combined
                current_paragraph = []
                current_words = 0
                current_sentences = 0

            # 超长句子单独成段
            result_paragraphs.append(sentence)
            continue

        # 计算与前后句的语义相似度
        prev_similarity = 1.0
        next_similarity = 1.0

        if i > 0:
            norm_i = np.linalg.norm(sentence_vectors[i])
            norm_prev = np.linalg.norm(sentence_vectors[i-1])
            if norm_i > 0 and norm_prev > 0:
                prev_similarity = np.dot(sentence_vectors[i], sentence_vectors[i-1]) / (norm_i * norm_prev)
            else:
                prev_similarity = 0.0

        if i < len(sentences) - 1:
            norm_i = np.linalg.norm(sentence_vectors[i])
            norm_next = np.linalg.norm(sentence_vectors[i+1])
            if norm_i > 0 and norm_next > 0:
                next_similarity = np.dot(sentence_vectors[i], sentence_vectors[i+1]) / (norm_i * norm_next)
            else:
                next_similarity = 0.0

        # 基于语义特征判断是否为过渡句
        is_transition = False
        if sentence_words < (min_words or 0) / 2:  # 短句
            # 与前后文的语义关联度差异较大，说明可能是过渡句
            if abs(prev_similarity - next_similarity) > 0.3:
                is_transition = True
                logger.debug(f"检测到可能的过渡句: {sentence}")

        # 处理段落分割
        should_start_new = False
        if min_words is not None and max_words is not None:
            if current_words + sentence_words > max_words:
                should_start_new = current_words >= min_words
            elif current_words >= min_words and prev_similarity < similarity_threshold:
                should_start_new = True
        else:
            should_start_new = current_sentences >= max_sentences

        # 如果是过渡句且当前段落已经满足最小长度要求，开始新段落
        if is_transition and current_words >= (min_words or 0):
            if current_paragraph:
                para = ''.join(current_paragraph) if language == 'Chinese' else ' '.join(current_paragraph)
                result_paragraphs.append(para.strip())
                current_paragraph = [sentence]
                current_words = sentence_words
                current_sentences = 1
        # 处理普通句子
        elif should_start_new and current_paragraph:
            para = ''.join(current_paragraph) if language == 'Chinese' else ' '.join(current_paragraph)
            if min_words is None or current_words >= min_words:
                result_paragraphs.append(para.strip())
                current_paragraph = [sentence]
                current_words = sentence_words
                current_sentences = 1
            else:
                # 如果当前段落太短，继续添加句子
                current_paragraph.append(sentence)
                current_words += sentence_words
                current_sentences += 1
        else:
            current_paragraph.append(sentence)
            current_words += sentence_words
            current_sentences += 1

    # 处理最后一个段落
    if current_paragraph:
        para = ''.join(current_paragraph) if language == 'Chinese' else ' '.join(current_paragraph)
        if min_words is None or current_words >= min_words:
            result_paragraphs.append(para.strip())
        elif result_paragraphs:
            last_para = result_paragraphs[-1]
            combined = (last_para + (' ' if language != 'Chinese' else '') + para)
            result_paragraphs[-1] = combined.strip()
        else:
            result_paragraphs.append(para.strip())

    return result_paragraphs

def read_file_with_encoding(file_path: str) -> str:
    """
    尝试使用不同的编码格式读取文件

    Args:
        file_path: 文件路径

    Returns:
        str: 文件内容
    """
    # 常见的中文编码格式
    encodings = ['utf-8', 'gb18030', 'gbk', 'gb2312', 'big5']

    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()
                logger.info(f"Successfully read file with {encoding} encoding")
                return content
        except UnicodeDecodeError:
            continue
        except Exception as e:
            logger.error(f"Error reading file with {encoding} encoding: {str(e)}")
            continue

    # 如果所有编码都失败了，尝试使用 chardet 检测编码
    try:
        import chardet
        with open(file_path, 'rb') as f:
            raw_data = f.read()
        detected = chardet.detect(raw_data)
        encoding = detected['encoding']

        if encoding:
            try:
                content = raw_data.decode(encoding)
                logger.info(f"Successfully read file with detected encoding: {encoding}")
                return content
            except UnicodeDecodeError:
                pass
    except ImportError:
        logger.warning("chardet not installed. Unable to detect file encoding.")
    except Exception as e:
        logger.error(f"Error detecting file encoding: {str(e)}")

    raise UnicodeDecodeError(f"Unable to read file {file_path} with any known encoding")

def clean_text(text: str) -> str:
    """清理文本"""
    # 统一全角/半角字符
    text = unicodedata.normalize('NFKC', text)

    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text)

    # 移除特殊字符
    text = re.sub(r'[^\w\s\u4e00-\u9fff.,!?，。！？""《》「」\-\—–]', '', text)

    # 统一标点符号
    text = text.replace('"', '"').replace('"', '"')  # 统一引号
    text = text.replace(''', "'").replace(''', "'")  # 统一单引号
    text = text.replace('…', '...')  # 统一省略号

    return text.strip()

def clean_filename(file_path: str, suffixes: List[str] = None) -> str:
    """
    从文件路径中移除特定后缀并返回清理后的文件路径

    Args:
        file_path (str): 原始文件路径
        suffixes (List[str], optional): 需要移除的后缀列表。如果为None，使用默认后缀列表

    Returns:
        str: 清理后的文件路径
    """
    # 默认需要移除的后缀
    default_suffixes = [
        '_prompts_images_timing',
        '_prompts_timing_images',
        '_prompts_images',
        '_timing',
        '_prompts'
    ]

    suffixes = suffixes or default_suffixes

    # 分离目录、文件名和扩展名
    directory = os.path.dirname(file_path)
    filename = os.path.basename(file_path)
    basename, ext = os.path.splitext(filename)

    # 检查并移除已知后缀
    clean_name = basename
    for suffix in suffixes:
        if basename.endswith(suffix):
            clean_name = basename[:-len(suffix)]
            break

    # 重新组合文件路径
    return os.path.join(directory, f"{clean_name}{ext}")

def calculate_token_count(text: str, encoding: str = DEFAULT_ENCODING) -> int:
    """计算文本Token数"""
    try:
        tokenizer = tiktoken.get_encoding(encoding)
        return len(tokenizer.encode(text))
    except Exception as e:
        logger.error(f"Token计算错误: {str(e)}")
        return 0

def extract_chapter_info(text: str, default_title: str = "开篇") -> dict:
    """从文本中提取章节信息

    Args:
        text: 需要处理的文本/标题
        default_title: 默认标题，当无法匹配到章节标题时使用

    Returns:
        dict: 包含章节信息的字典
    """
    # 使用与 generate_chapter_summary.py 相同的正则表达式
    chapter_pattern = re.compile(r'^第([零一二三四五六七八九十百千万\d]+)([章节回集卷])(.*)$')

    # 初始化返回结果
    result = {
        "title": default_title,
        "chapter_type": "章",
        "chapter_number": "0",
        "is_valid": False
    }

    # 如果输入为空，返回默认值
    if not text:
        return result

    # 尝试匹配章节标题
    match = chapter_pattern.match(text)
    if match:
        number_str = match.group(1)    # 章节数字
        chapter_type = match.group(2)  # 章节类型
        title_rest = match.group(3)    # 标题剩余部分

        result.update({
            "title": text,
            "chapter_number": number_str,
            "chapter_type": chapter_type,
            "is_valid": True
        })

    return result


def copy_script_files(source_file: str, theme: str):
    """复制脚本文件到目标目录"""
    target_dir = SCRIPT_DIR.format(theme=theme)
    os.makedirs(target_dir, exist_ok=True)

    def copy_file(src: str, dst: str):
        if os.path.abspath(src) != os.path.abspath(dst):
            shutil.copy2(src, dst)
            logger.info(f"已将文件 {src} 复制到 {dst}")
        else:
            logger.info(f"源文件和目标文件相同，跳过复制: {src}")

    main_target = os.path.join(target_dir, f"{theme}.txt")
    copy_file(source_file, main_target)

    source_json = os.path.splitext(source_file)[0] + '.json'
    if os.path.exists(source_json):
        json_target = os.path.join(target_dir, f"{theme}.json")
        copy_file(source_json, json_target)

        source_wav = os.path.splitext(source_file)[0] + '.wav'
        if os.path.exists(source_wav):
            wav_target = os.path.join(target_dir, f"{theme}.wav")
            copy_file(source_wav, wav_target)

    base_name_without_ext = os.path.splitext(os.path.basename(source_file))[0]
    for lang, code in LANGUAGE_CODES.items():
        if lang == "English":
            continue

        lang_file = os.path.join(os.path.dirname(source_file), f"{base_name_without_ext}_{code}.txt")
        if os.path.exists(lang_file):
            lang_target = os.path.join(target_dir, f"{theme}_{code}.txt")
            copy_file(lang_file, lang_target)

    today = datetime.date.today().strftime("%Y%m%d")
    os.makedirs(TOPIC_DIR, exist_ok=True)
    topics_file = os.path.join(TOPIC_DIR, f"topic_{today}.txt")

    try:
        with open(topics_file, 'a+') as f:
            f.seek(0, os.SEEK_END)
            if f.tell() == 0:
                f.write(f"{theme}\n")
            else:
                f.write(f"\n{theme}")
        logger.info(f"已将主题 '{theme}' 添加到文件 {topics_file}")
    except IOError as e:
        logger.error(f"无法写入文件 {topics_file}: {e}")


def write_temp_file(content: Union[str, dict, list], filename_prefix: str, base_dir: str = '.') -> str:
    """
    在指定目录的temp文件夹下写入文件，添加时间戳并根据内容类型选择文件扩展名

    Args:
        content (Union[str, dict, list]): 要写入的内容，可以是字符串、字典或列表
        filename_prefix (str): 文件名前缀
        base_dir (str): 基础目录路径，默认为当前目录

    Returns:
        str: 写入文件的完整路径
    """
    # 创建temp目录
    temp_dir = os.path.join(base_dir, 'temp')
    os.makedirs(temp_dir, exist_ok=True)

    # 生成时间戳
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')

    # 根据内容类型确定文件扩展名和处理方式
    if isinstance(content, (dict, list)) or (
        isinstance(content, str) and content.strip().startswith('[') and content.strip().endswith(']')
    ):
        file_ext = '.json'
        # 如果内容是字符串形式的JSON，尝试解析它
        if isinstance(content, str):
            try:
                content = json.loads(content)
            except json.JSONDecodeError:
                file_ext = '.txt'
        content = json.dumps(content, ensure_ascii=False, indent=2)
    else:
        file_ext = '.txt'
        content = str(content)

    # 构建完整的文件路径（包含时间戳）
    file_path = os.path.join(temp_dir, f"{filename_prefix}_{timestamp}{file_ext}")

    try:
        # 写入文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

        logger.info(f"成功写入文件: {file_path}")
        return file_path
    except Exception as e:
        logger.error(f"写入文件失败: {str(e)}")
        raise


def normalize_language(language: str) -> str:
    """
    将输入的语言标准化为两字母代码
    """
    language = language.lower()
    for lang, code in LANGUAGE_CODES.items():
        if language in {lang.lower(), code}:
            return code
    raise ValueError(f"不支持的语言: {language}")


def split_into_chunks(text: str, max_tokens: int = 2000, language: str = 'zh') -> List[str]:
    """
    将文本按token数量分块，确保句子完整性并尽量均匀分配

    Args:
        text: 要分块的文本
        max_tokens: 每块最大token数
        language: 文本语言，默认为中文 'zh'

    Returns:
        List[str]: 分块后的文本列表
    """
    # 计算总token数和目标块大小
    total_tokens = calculate_token_count(text)
    chunk_number = max(1, (total_tokens + max_tokens - 1) // max_tokens)
    target_chunk_size = total_tokens // chunk_number

    logger.debug(f"总tokens: {total_tokens}, 目标块数: {chunk_number}, "
                f"目标块大小: {target_chunk_size}, 语言: {language}")

    # 使用传入的语言参数进行分句
    sentences = smart_paragraph_split(text, language=language)
    logger.debug(f"分割后的句子数量: {len(sentences)}")

    chunks = []
    current_chunk = []
    current_tokens = 0

    for sentence in sentences:
        sentence_tokens = calculate_token_count(sentence)

        # 如果当前块已经达到或超过目标大小，开始新的块
        if current_tokens >= target_chunk_size and current_chunk:
            chunks.append(" ".join(current_chunk).strip())
            current_chunk = []
            current_tokens = 0

        # 将新句子添加到当前块
        current_chunk.append(sentence)
        current_tokens += sentence_tokens

    # 添加最后一个块
    if current_chunk:
        chunks.append(" ".join(current_chunk).strip())

    logger.info(f"文本已分割为 {len(chunks)} 个块，平均大小: "
                f"{sum(calculate_token_count(c) for c in chunks) / len(chunks):.0f} tokens")

    return chunks

def normalize_words(text: str) -> str:
    """
    规范化文本，将特殊字符替换为下划线，处理 Unicode 字符

    Args:
        text (str): 需要规范化的文本
            例如: "My Text (1)" 或 "user's text" 或 "résumé"

    Returns:
        str: 规范化后的文本
            例如: "My_Text_1" 或 "users_text" 或 "resume"

    Raises:
        ValueError: 如果输入为空
    """
    if not text:
        raise ValueError("输入文本不能为空")

    # Unicode 标准化处理：将组合字符转换为单个字符
    # NFKD 将字符分解为基本字符和组合字符
    text = unicodedata.normalize('NFKD', text)

    # 移除变音符号和其他组合字符
    text = ''.join(c for c in text if not unicodedata.combining(c))

    # 替换非字母数字和连字符的字符为下划线
    # 保留基本的ASCII字母数字字符和连字符
    normalized = re.sub(r'[^\x00-\x7F\w\-]|[^\w\-]', '_', text)

    # 合并连续的下划线
    normalized = re.sub(r'_+', '_', normalized)

    # 移除首尾的下划线
    return normalized.strip('_')

def normalized_filename(filename: str) -> str:
    """
    规范化文件名（不包含路径），将特殊字符替换为下划线

    Args:
        filename (str): 需要规范化的文件名

    Returns:
        str: 规范化后的文件名

    Raises:
        ValueError: 如果输入为空
    """
    if not filename:
        raise ValueError("文件名不能为空")

    # 只获取文件名部分（移除路径）
    filename = os.path.basename(filename)

    # 如果是特殊路径标记，直接返回
    if filename in {'.', '..'}:
        return filename

    # 处理带扩展名的文件
    if '.' in filename:
        name, ext = os.path.splitext(filename)
        # 使用新的 normalize_words 函数处理文件名部分
        normalized = normalize_words(name)
        return f"{normalized}{ext}"
    else:
        # 处理没有扩展名的文件名
        return normalize_words(filename)

def get_image_paths(directory: str, extensions: Optional[List[str]] = None) -> List[str]:
    """
    获取指定目录下的所有图像文件路径。

    :param directory: 图像目录路径
    :param extensions: 支持的图像扩展名列表
    :return: 图像文件路径列表
    """
    if extensions is None:
        extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.gif', '.webp']
    image_paths = [
        os.path.join(directory, img)
        for img in os.listdir(directory)
        if os.path.splitext(img.lower())[1] in extensions
    ]
    logger.info(f"找到 {len(image_paths)} 张图像。")
    return image_paths


def process_script(
    script_path: str,
    min_words: int = MIN_WORDS,
    max_words: int = MAX_WORDS,
    using_anchor: bool = False
) -> List[Dict[str, Any]]:
    """
    处理脚本文件，将文本分段并准备图片匹配

    Args:
        script_path: 脚本文件路径
        min_words: 每段最小字数（默认60，约对应25-30秒语音）
        max_words: 每段最大字数（默认75，约对应30-35秒语音）
        using_anchor: 是否使用 null 占位符作为锚点
    """
    try:
        # 读取脚本文件
        text = read_file_with_encoding(script_path)

        # 使用 smart_paragraph_split 进行分段
        segments = smart_paragraph_split(
            text,
            min_sentences=1,
            max_sentences=2,
            min_words=min_words,
            max_words=max_words
        )

        logger.info(f"文本已分成 {len(segments)} 个段落")

        # 构建段落数据结构
        paragraphs = []
        for i, segment in enumerate(segments):
            paragraph = {
                'segment_id': i + 1,
                'paragraph_text': segment,
                'NumberImage': 0 if (i == 0 and using_anchor) else 1
            }
            paragraphs.append(paragraph)

        return paragraphs

    except Exception as e:
        logger.error(f"处理脚本文件时出错：{e}")
        raise

def resize_large_image(image_path: str, theme: str, threshold_kb: int = IMAGE_SIZE_DESCALE_THRESHOLD) -> str:
    """
    如果图片大小超过阈值，则调整其大小。
    调整大小后，原始的大文件会被移动到 RESIZED_IMAGE_DIR 定义的子目录中，
    调整后的图片会替换原始路径的文件。

    Args:
        image_path (str): 输入图片路径。
        theme (str): 当前处理的主题，用于构建正确的 resized 目录。
        threshold_kb (int): 文件大小阈值 (KB)。

    Returns:
        str: 处理后图片的路径（如果发生调整，则是新文件的路径；否则是原始路径）。
             如果处理失败，返回原始路径。
    """
    try:
        if not os.path.exists(image_path):
            logger.error(f"[Resize] 文件未找到: {image_path}")
            return image_path

        file_size_kb = os.path.getsize(image_path) / 1024

        if file_size_kb > threshold_kb:
            logger.info(f"[Resize] 图片 {os.path.basename(image_path)} ({file_size_kb/1024:.2f}MB) 超过阈值 {threshold_kb/1024:.2f}MB，开始调整大小...")

            dir_path = os.path.dirname(image_path)
            base_name = os.path.basename(image_path)
            name, ext = os.path.splitext(base_name)
            # 确保使用常见的图片扩展名，如 .jpg
            output_ext = '.jpg' if ext.lower() not in ['.jpg', '.jpeg', '.png'] else ext

            # 1. 定义临时输出路径和原始文件移动目标路径
            # 使用 theme 参数格式化 RESIZED_IMAGE_DIR
            resized_subdir = RESIZED_IMAGE_DIR.format(theme=theme)
            os.makedirs(resized_subdir, exist_ok=True)
            original_move_dest = os.path.join(resized_subdir, base_name) # 原始文件移到这里
            # 使用临时文件名避免覆盖正在读取的文件 (仍在原始目录)
            temp_resized_path = os.path.join(dir_path, f"{name}_temp_resized{output_ext}")

            # 2. 执行 ffmpeg 调整大小命令
            resize_cmd = [
                "ffmpeg", "-y",
                "-i", image_path,
                "-vf", "scale=1920:-1", # 调整宽度为1920，保持宽高比
                "-q:v", "90",          # 设置输出质量 (JPEG)
                temp_resized_path
            ]
            logger.debug(f"[Resize] 执行命令: {' '.join(resize_cmd)}")
            result = subprocess.run(resize_cmd, check=True, capture_output=True, text=True)
            if result.stderr:
                 logger.debug(f"FFmpeg stderr: {result.stderr}")

            # 3. 验证调整大小后的文件是否存在且非空
            if not os.path.exists(temp_resized_path) or os.path.getsize(temp_resized_path) == 0:
                logger.error(f"[Resize] ffmpeg 调整大小失败，未生成有效输出文件: {temp_resized_path}")
                if os.path.exists(temp_resized_path): # 清理空的临时文件
                    os.remove(temp_resized_path)
                return image_path # 返回原始路径

            # 4. 移动原始大文件到 resized 子目录
            try:
                # 防止目标文件已存在导致 shutil.move 失败
                counter = 1
                final_dest = original_move_dest
                while os.path.exists(final_dest):
                    final_dest = os.path.join(resized_subdir, f"{name}_{counter}{ext}")
                    counter += 1
                if final_dest != original_move_dest:
                     logger.warning(f"目标文件已存在，原始文件将移动到: {final_dest}")

                shutil.move(image_path, final_dest)
                logger.info(f"[Resize] 原始大文件已移动到: {final_dest}")
            except Exception as move_err:
                logger.error(f"[Resize] 移动原始文件 {image_path} 到 {final_dest} 失败: {move_err}")
                # 尝试清理临时文件
                if os.path.exists(temp_resized_path):
                    os.remove(temp_resized_path)
                return image_path # 返回原始路径，因为操作未完成

            # 5. 将调整大小后的临时文件重命名/替换为原始文件路径
            try:
                os.replace(temp_resized_path, image_path)
                logger.info(f"[Resize] 调整大小完成，新文件位于: {image_path}")
                return image_path # 返回原始路径，现在它指向调整后的文件
            except Exception as replace_err:
                logger.error(f"[Resize] 重命名/替换 {temp_resized_path} 到 {image_path} 失败: {replace_err}")
                # 严重错误：原始文件已移动，但替换失败。尝试恢复。
                try:
                    shutil.move(final_dest, image_path)
                    logger.warning(f"[Resize] 尝试恢复原始文件从 {final_dest} 到 {image_path}")
                except Exception as restore_err:
                    logger.critical(f"[Resize] 恢复原始文件失败: {restore_err}. 文件可能位于 {final_dest}")
                # 清理临时文件
                if os.path.exists(temp_resized_path):
                    os.remove(temp_resized_path)
                return image_path # 返回原始路径（可能现在是恢复的原始文件，或为空）

        else:
            logger.debug(f"[Resize] 图片 {os.path.basename(image_path)} ({file_size_kb/1024:.2f}MB) 小于阈值，跳过调整大小。")
            return image_path

    except subprocess.CalledProcessError as e:
        logger.error(f"[Resize] ffmpeg 命令执行失败 for {image_path}: {e.stderr}")
        return image_path # ffmpeg失败，返回原始路径
    except Exception as e:
        logger.error(f"[Resize] 处理图片 {image_path} 时发生错误: {e}")
        logger.debug(f"详细错误信息:", exc_info=True)
        return image_path # 其他错误，返回原始路径

def standardize_image_for_video(image_path: str, theme: str) -> str:
    """对中等大小的图片进行标准化处理，防止视频抖动

    Args:
        image_path: 输入图片路径
        theme: 当前处理的主题，用于构建正确的 resized 目录

    Returns:
        str: 标准化处理后的图片路径
    """
    try:
        if not os.path.exists(image_path):
            logger.error(f"[Standardize] 文件未找到: {image_path}")
            return image_path

        # 构建输出文件名和路径
        base_name = os.path.basename(image_path)
        name, ext = os.path.splitext(base_name)

        # 使用 theme 参数格式化 RESIZED_IMAGE_DIR
        standardized_dir = RESIZED_IMAGE_DIR.format(theme=theme)
        os.makedirs(standardized_dir, exist_ok=True)

        # 标准化后的文件路径
        output_path = os.path.join(standardized_dir, f"{name}_standardized.jpg")

        # 如果不是调试模式，优先检查是否已有标准化结果
        if not UPSCALE_IMAGE_DEBUG and os.path.exists(output_path):
            logger.debug(f"[Standardize] 发现已有标准化结果，跳过处理: {output_path}")
            return output_path

        # 执行标准化命令：统一格式、分辨率和色彩空间
        cmd = [
            "ffmpeg", "-y",
            "-i", image_path,
            "-vf", "scale=1920:1080:force_original_aspect_ratio=decrease,pad=1920:1080:(ow-iw)/2:(oh-ih)/2:black,format=yuv420p",
            "-q:v", "85",  # 设置较高质量
            "-pix_fmt", "yuv420p",  # 强制使用标准色彩空间
            output_path
        ]

        logger.debug(f"[Standardize] 执行标准化命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        if result.stderr:
            logger.debug(f"FFmpeg stderr: {result.stderr}")

        if not os.path.exists(output_path):
            logger.error(f"[Standardize] 标准化操作失败，输出文件不存在: {output_path}")
            return image_path

        logger.info(f"[Standardize] 图片标准化成功: {output_path}")
        return output_path

    except subprocess.CalledProcessError as e:
        logger.error(f"[Standardize] 标准化命令执行失败 for {image_path}: {e.stderr}")
        return image_path
    except Exception as e:
        logger.error(f"[Standardize] 处理图片 {image_path} 时发生未知错误: {e}")
        logger.debug(f"详细错误信息:", exc_info=True)
        return image_path

# Moved from generate_video.py
def upscale_image(image_path: str, theme: str) -> str:
    """使用 realesrgan-ncnn-vulkan 对图片进行 4x 放大，
    如果图片过大，则先调用 resize_large_image 进行调整。
    对于中等大小的图片，进行标准化处理以防止视频抖动。
    调整和放大后的图片都保存在 themed RESIZED_IMAGE_DIR 中。

    Args:
        image_path: 输入图片路径
        theme: 当前处理的主题，用于构建正确的 resized 目录

    Returns:
        str: 最终处理后的图片路径 (可能是原始路径、调整大小后的路径或放大后的路径)
    """
    try:
        if not os.path.exists(image_path):
            logger.error(f"[Upscale] 文件未找到: {image_path}")
            return image_path

        # 检查原始图片大小
        original_file_size_kb = os.path.getsize(image_path) / 1024

        # 1. 处理过大的图片或需要标准化的中等大小图片
        current_image_path = image_path # 开始处理的路径
        needs_standardization = False

        # 使用从 config 导入的阈值
        if original_file_size_kb > IMAGE_SIZE_DESCALE_THRESHOLD:
            logger.info(f"[Upscale] 图片 {os.path.basename(image_path)} ({original_file_size_kb/1024:.2f}MB) 超过阈值，"
                        f"调用 resize_large_image 进行调整...")
            try:
                # 调用本文件中的 resize_large_image 函数处理大图片
                current_image_path = resize_large_image(image_path, theme=theme)
                if current_image_path != image_path:
                    logger.info(f"[Upscale] 图片已由 resize_large_image 调整大小，新路径: {current_image_path}")
                else:
                    logger.info(f"[Upscale] resize_large_image 未改变路径 (可能调整失败或无需调整)")

                # 如果路径改变了，更新大小信息以决定是否放大
                if current_image_path != image_path and os.path.exists(current_image_path):
                     file_size_kb_after_resize = os.path.getsize(current_image_path) / 1024
                     logger.debug(f"[Upscale] 调整后图片大小: {file_size_kb_after_resize/1024:.2f}MB")
                else:
                    # 如果调整失败或未调整，使用原始大小判断是否放大
                     file_size_kb_after_resize = original_file_size_kb

            except Exception as resize_err:
                logger.error(f"[Upscale] 调用 resize_large_image 失败: {resize_err}. 将尝试使用原始图片进行放大判断。")
                current_image_path = image_path # 出错则回退到原始路径
                file_size_kb_after_resize = original_file_size_kb
        elif original_file_size_kb > 800:  # 对于800KB以上的中等大小图片进行标准化处理
            logger.info(f"[Upscale] 图片 {os.path.basename(image_path)} ({original_file_size_kb/1024:.2f}MB) 需要标准化处理以防止抖动...")
            needs_standardization = True
            file_size_kb_after_resize = original_file_size_kb
        else:
             # 如果未调整大小，调整后的大小就是原始大小
             file_size_kb_after_resize = original_file_size_kb


        # 2. 处理标准化需求或决定是否进行放大
        if needs_standardization:
            # 对中等大小图片进行标准化处理
            return standardize_image_for_video(current_image_path, theme)

        # 使用从 config 导入的阈值决定是否放大
        should_upscale = file_size_kb_after_resize < IMAGE_SIZE_UPSCALE_THRESHOLD

        if not should_upscale:
            logger.debug(f"[Upscale] 图片 {os.path.basename(current_image_path)} "
                         f"(大小 {file_size_kb_after_resize/1024:.2f}MB) 不满足放大条件 (阈值 < {IMAGE_SIZE_UPSCALE_THRESHOLD/1024:.2f}MB)，跳过放大。")
            return current_image_path # 返回当前路径（可能是原始的，也可能是调整过的）

        # 3. 执行放大操作
        logger.info(f"[Upscale] 图片 {os.path.basename(current_image_path)} "
                    f"(大小 {file_size_kb_after_resize/1024:.2f}MB) 满足条件，开始放大...")

        # 构建输出文件名和路径 (统一放入 themed RESIZED_IMAGE_DIR)
        # dir_path = os.path.dirname(current_image_path) # 不需要了，输出目录直接用 RESIZED_IMAGE_DIR
        base_name = os.path.basename(current_image_path)
        name, ext = os.path.splitext(base_name)

        # 使用 theme 参数格式化 RESIZED_IMAGE_DIR (从 config 导入)
        upscaled_dir = RESIZED_IMAGE_DIR.format(theme=theme)
        os.makedirs(upscaled_dir, exist_ok=True)

        # 放大后的文件路径
        output_path = os.path.join(upscaled_dir, f"{name}_4x{ext}")

        # 如果不是调试模式，优先检查是否已有放大结果 (使用从 config 导入的 DEBUG 变量)
        if not UPSCALE_IMAGE_DEBUG and os.path.exists(output_path):
            logger.debug(f"[Upscale] 发现已有放大结果，跳过放大处理: {output_path}")
            return output_path

        # 执行放大命令 (使用 current_image_path 作为输入)
        cmd = [
            "realesrgan-ncnn-vulkan",
            "-i", current_image_path, # 使用可能被 resize_large_image 修改过的路径
            "-o", output_path,
            "-n", "realesrgan-x4plus",
            "-s", "4",
            "-m", "/opt/homebrew/share/realesrgan-ncnn-vulkan/models" # TODO: Consider making this configurable
        ]

        logger.debug(f"[Upscale] 执行放大命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        if result.stderr:
             logger.debug(f"realesrgan stderr: {result.stderr}")


        if not os.path.exists(output_path):
            logger.error(f"[Upscale] 放大操作失败，输出文件不存在: {output_path}")
            return current_image_path # 放大失败，返回放大前的路径

        logger.info(f"[Upscale] 图片放大成功: {output_path}")
        return output_path

    except subprocess.CalledProcessError as e:
        logger.error(f"[Upscale] 放大命令执行失败 for {image_path}: {e.stderr}")
        # 尝试返回调整后的路径（如果存在），否则返回原始路径
        return current_image_path if 'current_image_path' in locals() and os.path.exists(current_image_path) else image_path
    except Exception as e:
        logger.error(f"[Upscale] 处理图片 {image_path} 时发生未知错误: {e}")
        logger.debug(f"详细错误信息:", exc_info=True)
        # 如果发生未知错误，谨慎起见返回原始输入路径
        return image_path

