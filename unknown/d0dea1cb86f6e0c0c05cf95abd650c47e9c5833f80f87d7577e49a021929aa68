#!/usr/bin/env python3
"""
完整流程测试脚本
测试从小说到视频的完整生成流程
"""

import os
import sys
import json
import subprocess
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import logger, ANIMATION_DRAMA_DIR, ANIMATION_EPISODES_DIR

class PipelineValidator:
    """流程验证器"""

    def __init__(self):
        self.project_name = "save_witch_whole"
        self.base_dir = Path(ANIMATION_DRAMA_DIR)
        self.episodes_dir = Path(ANIMATION_EPISODES_DIR) / self.project_name
        self.raw_text_dir = self.base_dir / "raw_text"

        self.test_results = {
            "timestamp": datetime.now().isoformat(),
            "stages": {},
            "overall_success": False
        }

    def test_stage1_summary_generation(self) -> bool:
        """测试阶段1：摘要生成"""
        logger.info("测试阶段1：摘要生成")

        # 检查输入文件
        novel_file = self.raw_text_dir / f"{self.project_name}.txt"
        if not novel_file.exists():
            logger.error(f"小说文件不存在: {novel_file}")
            return False

        # 检查输出文件
        summary_file = self.raw_text_dir / f"{self.project_name}.json"
        group_file = self.raw_text_dir / f"{self.project_name}_group.json"
        outline_file = self.raw_text_dir / f"{self.project_name}_story_outline.json"

        files_exist = all([
            summary_file.exists(),
            group_file.exists(),
            outline_file.exists()
        ])

        if files_exist:
            logger.info("✅ 阶段1文件检查通过")
            # 验证文件内容
            try:
                with open(summary_file, 'r', encoding='utf-8') as f:
                    summary_data = json.load(f)
                logger.info(f"摘要文件包含 {len(summary_data)} 个章节")
                return True
            except Exception as e:
                logger.error(f"摘要文件格式错误: {e}")
                return False
        else:
            logger.error("❌ 阶段1文件缺失")
            return False

    def test_stage2_episode_generation(self) -> bool:
        """测试阶段2：剧集生成"""
        logger.info("测试阶段2：剧集生成")

        # 检查剧集文件
        episode_files = list(self.episodes_dir.glob("episode_*.json"))
        episode_files = [f for f in episode_files if "_timing" not in f.name and "_config" not in f.name and "_segments" not in f.name]

        if not episode_files:
            logger.error("❌ 没有找到剧集文件")
            return False

        logger.info(f"找到 {len(episode_files)} 个剧集文件")

        # 验证剧集文件结构
        for episode_file in episode_files:
            try:
                with open(episode_file, 'r', encoding='utf-8') as f:
                    episode_data = json.load(f)

                # 检查是否有 ep 嵌套结构
                if "ep" in episode_data:
                    ep_data = episode_data["ep"]
                    # 检查必要字段
                    required_fields = ["ep_n", "t", "c", "scenes"]
                    if not all(field in ep_data for field in required_fields):
                        logger.error(f"剧集文件 {episode_file.name} 缺少必要字段")
                        return False

                    scenes = ep_data.get("scenes", [])
                    logger.info(f"{episode_file.name}: {len(scenes)} 个场景")
                else:
                    # 旧格式检查
                    required_fields = ["n", "t", "c", "s"]
                    if not all(field in episode_data for field in required_fields):
                        logger.error(f"剧集文件 {episode_file.name} 缺少必要字段")
                        return False

                    scenes = episode_data.get("s", [])
                    logger.info(f"{episode_file.name}: {len(scenes)} 个场景")

            except Exception as e:
                logger.error(f"剧集文件 {episode_file.name} 格式错误: {e}")
                return False

        logger.info("✅ 阶段2文件检查通过")
        return True

    def test_stage3_audio_generation(self) -> bool:
        """测试阶段3：音频生成"""
        logger.info("测试阶段3：音频生成")

        episode_files = list(self.episodes_dir.glob("episode_*.json"))
        episode_files = [f for f in episode_files if "_timing" not in f.name and "_config" not in f.name and "_segments" not in f.name]

        audio_success = 0
        timing_success = 0

        for episode_file in episode_files:
            episode_name = episode_file.stem
            audio_file = episode_file.parent / f"{episode_name}.wav"
            timing_file = episode_file.parent / f"{episode_name}_timing.json"

            if audio_file.exists() and audio_file.stat().st_size > 0:
                audio_success += 1
                logger.info(f"✅ {episode_name}.wav 存在")
            else:
                logger.warning(f"❌ {episode_name}.wav 不存在或为空")

            if timing_file.exists():
                timing_success += 1
                logger.info(f"✅ {episode_name}_timing.json 存在")

                # 验证时间轴文件结构
                try:
                    with open(timing_file, 'r', encoding='utf-8') as f:
                        timing_data = json.load(f)

                    scenes = timing_data.get("s", [])
                    for scene in scenes:
                        if "scene_start_time" not in scene or "scene_end_time" not in scene:
                            logger.warning(f"{episode_name}_timing.json 缺少时间信息")
                            break

                except Exception as e:
                    logger.error(f"时间轴文件 {timing_file.name} 格式错误: {e}")
            else:
                logger.warning(f"❌ {episode_name}_timing.json 不存在")

        success_rate = (audio_success + timing_success) / (len(episode_files) * 2)
        logger.info(f"阶段3成功率: {success_rate:.1%} ({audio_success + timing_success}/{len(episode_files) * 2})")

        return success_rate >= 0.5  # 至少50%成功率

    def test_stage4_image_generation(self) -> bool:
        """测试阶段4：图片生成"""
        logger.info("测试阶段4：图片生成")

        episode_files = list(self.episodes_dir.glob("episode_*.json"))
        episode_files = [f for f in episode_files if "_timing" not in f.name and "_config" not in f.name and "_segments" not in f.name]

        image_success = 0
        total_expected = 0

        for episode_file in episode_files:
            episode_name = episode_file.stem
            image_dir = self.episodes_dir / "images" / episode_name

            if image_dir.exists():
                image_files = list(image_dir.glob("*.png"))
                image_success += len(image_files)
                logger.info(f"✅ {episode_name}: 找到 {len(image_files)} 张图片")

                # 计算期望的图片数量（基于场景数）
                try:
                    with open(episode_file, 'r', encoding='utf-8') as f:
                        episode_data = json.load(f)
                    scenes = episode_data.get("s", [])
                    total_expected += len(scenes)
                except:
                    pass
            else:
                logger.warning(f"❌ {episode_name}: 图片目录不存在")

        if total_expected > 0:
            success_rate = image_success / total_expected
            logger.info(f"阶段4成功率: {success_rate:.1%} ({image_success}/{total_expected})")
            return success_rate >= 0.3  # 至少30%成功率
        else:
            logger.warning("无法确定期望的图片数量")
            return image_success > 0

    def test_stage5_video_generation(self) -> bool:
        """测试阶段5：视频生成"""
        logger.info("测试阶段5：视频生成")

        episode_files = list(self.episodes_dir.glob("episode_*.json"))
        episode_files = [f for f in episode_files if "_timing" not in f.name and "_config" not in f.name and "_segments" not in f.name]

        video_success = 0

        for episode_file in episode_files:
            episode_name = episode_file.stem
            video_file = episode_file.parent / f"{episode_name}_video.mp4"

            if video_file.exists() and video_file.stat().st_size > 0:
                video_success += 1
                logger.info(f"✅ {episode_name}_video.mp4 存在")
            else:
                logger.warning(f"❌ {episode_name}_video.mp4 不存在或为空")

        success_rate = video_success / len(episode_files) if episode_files else 0
        logger.info(f"阶段5成功率: {success_rate:.1%} ({video_success}/{len(episode_files)})")

        return success_rate >= 0.5  # 至少50%成功率

    def test_environment_setup(self) -> bool:
        """测试环境配置"""
        logger.info("测试环境配置")

        # 检查环境变量
        required_env_vars = ["AZURE_SUBSCRIPTION_KEY"]
        missing_vars = []

        for var in required_env_vars:
            if not os.getenv(var):
                missing_vars.append(var)

        if missing_vars:
            logger.warning(f"缺少环境变量: {missing_vars}")

        # 检查ComfyUI连接
        try:
            import requests
            response = requests.get("http://127.0.0.1:8188", timeout=5)
            logger.info("✅ ComfyUI 服务可访问")
            comfyui_available = True
        except:
            logger.warning("❌ ComfyUI 服务不可访问")
            comfyui_available = False

        # 检查FFmpeg
        try:
            result = subprocess.run(["ffmpeg", "-version"],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                logger.info("✅ FFmpeg 可用")
                ffmpeg_available = True
            else:
                logger.warning("❌ FFmpeg 不可用")
                ffmpeg_available = True
        except:
            logger.warning("❌ FFmpeg 不可用")
            ffmpeg_available = False

        return len(missing_vars) == 0 and comfyui_available and ffmpeg_available

    def run_full_test(self) -> dict:
        """运行完整测试"""
        logger.info("开始完整流程测试")

        # 测试各个阶段
        stages = [
            ("environment", self.test_environment_setup),
            ("summary", self.test_stage1_summary_generation),
            ("episodes", self.test_stage2_episode_generation),
            ("audio", self.test_stage3_audio_generation),
            ("images", self.test_stage4_image_generation),
            ("videos", self.test_stage5_video_generation)
        ]

        passed_stages = 0
        for stage_name, test_func in stages:
            try:
                result = test_func()
                self.test_results["stages"][stage_name] = {
                    "passed": result,
                    "error": None
                }
                if result:
                    passed_stages += 1
                    logger.info(f"✅ {stage_name} 测试通过")
                else:
                    logger.warning(f"❌ {stage_name} 测试失败")
            except Exception as e:
                logger.error(f"❌ {stage_name} 测试出错: {e}")
                self.test_results["stages"][stage_name] = {
                    "passed": False,
                    "error": str(e)
                }

        # 计算总体成功率
        success_rate = passed_stages / len(stages)
        self.test_results["overall_success"] = success_rate >= 0.7  # 70%通过率
        self.test_results["success_rate"] = success_rate

        logger.info(f"测试完成，总体成功率: {success_rate:.1%}")

        return self.test_results


def main():
    """主函数"""
    validator = PipelineValidator()
    results = validator.run_full_test()

    # 保存测试结果
    results_file = "pipeline_test_results.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)

    print("\n" + "="*60)
    print("完整流程测试报告")
    print("="*60)

    print(f"\n📊 总体成功率: {results['success_rate']:.1%}")
    print(f"🎯 测试结果: {'✅ 通过' if results['overall_success'] else '❌ 失败'}")

    print(f"\n📋 各阶段详情:")
    for stage_name, stage_result in results["stages"].items():
        status = "✅ 通过" if stage_result["passed"] else "❌ 失败"
        print(f"  {stage_name}: {status}")
        if stage_result["error"]:
            print(f"    错误: {stage_result['error']}")

    print(f"\n📄 详细结果已保存至: {results_file}")

    # 返回状态码
    return 0 if results["overall_success"] else 1


if __name__ == "__main__":
    sys.exit(main())
