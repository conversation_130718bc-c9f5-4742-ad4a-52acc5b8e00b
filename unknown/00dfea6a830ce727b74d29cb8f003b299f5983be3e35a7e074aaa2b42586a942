# 本地视频检索系统

一个本地运行的视频检索系统，能够对视频进行智能分析、切分、索引和检索。

## 主要功能

- **视频下载**: 支持从 Pexels 和 Pixabay API 下载视频。
- **手动导入**: 支持导入本地视频文件，并从关联的 `.tags.txt` 文件读取标签。
- **智能切分**:
    - 融合语音、视觉和文本语义信息，将长视频智能切分为具有逻辑连贯性的短片段。
    - 支持配置最短切片时长，例如，短于1分钟的视频不进行切分。
- **多模态索引**:
    - 对视频片段提取视觉特征（例如使用 CLIP 模型）。
    - 对视频片段提取文本特征（基于 Whisper 转录的文本和标签，使用句子嵌入模型）。
    - 使用 FAISS 构建高效的向量索引。
- **标签化管理**: 支持为视频和视频片段关联标签，用于辅助检索和内容组织。
- **高效检索**: 用户可以通过文本描述或标签组合快速查找相关的视频片段。
- **并行处理**: 下载和部分处理步骤采用异步操作，提高效率。

## 核心技术栈

- **音视频处理**: FFmpeg
- **语音转文字**: OpenAI Whisper
- **场景检测**: PySceneDetect
- **文本处理与嵌入**: SentenceTransformers, NLTK
- **视觉嵌入**: CLIP (Hugging Face Transformers)
- **向量索引与搜索**: FAISS
- **数据库**: SQLite (FTS5 for tag search)
- **API交互**: httpx (异步 HTTP 请求)
- **配置管理**: `python-dotenv`
- **异步任务**: `asyncio`
- **速率控制**: 自定义令牌桶实现

## 项目结构

```
video_retrieval/
├── video_retrieval_main.py       # 主程序入口 (CLI)
├── video_config.py             # 项目核心配置文件 (路径, API密钥, 模型参数等)
├── metadata_db.py              # SQLite数据库交互 (schema定义, 增删改查)
├── utils.py                    # 通用工具函数 (FFmpeg操作, 文件处理等)
├── token_bucket.py             # API请求速率控制 (令牌桶实现)
├── phase0_splitting.py         # 视频智能切分逻辑
├── phase1_indexing.py          # 特征提取与FAISS索引构建逻辑
├── phase2_retrieval.py         # 查询处理与检索逻辑
├── downloader_common.py        # 下载器通用功能 (下载,元数据保存)
├── pexels_downloader.py        # Pexels视频下载器
├── pixabay_downloader.py       # Pixabay视频下载器
├── manual_video_importer.py    # 手动导入视频及标签脚本
├── requirements.txt            # Python依赖列表
├── README.md                   # 本文档
└── data/                         # (此目录通常在项目外，由video_config.py指定路径)
    ├── long_videos/            # 存放原始长视频 (包括下载的和手动导入的)
    ├── clips/                  # 切分后的短视频片段 (按原视频名分子目录存放)
    ├── frames_cache/           # 提取的视频帧缓存 (按clip_id分子目录存放)
    ├── audio_cache/            # 提取的音频缓存 (按clip_id分子目录存放)
    ├── indexes/                # FAISS索引文件及其他索引数据
    │   ├── embeddings_data/    # (可选) 单独存储的NPY格式嵌入向量
    │   ├── visual_faiss.index
    │   └── transcript_faiss.index
    ├── database/               # SQLite数据库文件
    │   └── video_library.sqlite3
    ├── config/                 # 配置文件目录
    │   └── keywords.txt        # API下载关键词列表
    └── manual_imports/         # 手动导入视频的源文件夹
    └── .env                    # (可选) 存放API密钥等环境变量
```

## 安装与配置

1.  **克隆仓库** (如果适用)
2.  **环境准备**:
    *   建议使用 Python 3.9+
    *   安装 FFmpeg: `brew install ffmpeg` (macOS) 或对应系统的安装方式。
3.  **创建并激活虚拟环境**:
    ```bash
    python3 -m venv venv
    source venv/bin/activate  # macOS/Linux
    # venv\\Scripts\\activate    # Windows
    ```
4.  **安装依赖**:
    ```bash
    pip install -r requirements.txt
    ```
5.  **API密钥配置**:
    *   在 `video_config.py` 中指定的 `VIDEO_ROOT_DIR` 下创建 `.env` 文件 (例如 `ai_video_data/.env`)。
    *   在 `.env` 文件中添加您的API密钥:
        ```env
        PEXELS_API_KEY="YOUR_PEXELS_API_KEY"
        PIXABAY_API_KEY="YOUR_PIXABAY_API_KEY"
        ```
    *   系统会自动加载 `.env` 文件。或者，您也可以直接在操作系统中设置环境变量。
6.  **关键词配置**:
    *   编辑 `data/config/keywords.txt` (路径由 `video_config.KEYWORDS_FILE_PATH` 定义)，每行输入一个希望从Pexels/Pixabay下载视频的关键词。

## 使用方法 (命令行)

所有命令通过 `video_retrieval_main.py` 执行。

1.  **下载视频 (可选)**:
    *   **Pexels**:
        ```bash
        python video_retrieval/video_retrieval_main.py download_videos --source pexels --keywords "nature,city" --max_per_keyword 10
        # 或者使用 keywords.txt 文件:
        python video_retrieval/video_retrieval_main.py download_videos --source pexels --use_keywords_file --max_per_keyword 5
        ```
    *   **Pixabay**:
        ```bash
        python video_retrieval/video_retrieval_main.py download_videos --source pixabay --keywords "crime" --max_per_keyword 10
        ```
    *   下载所有可用源:
        ```bash
        python video_retrieval/video_retrieval_main.py download_videos --source all --use_keywords_file --max_per_keyword 3
        ```

2.  **手动导入视频 (可选)**:
    *   将视频文件 (如 `.mp4`) 和可选的标签文件 (同名，以 `.tags.txt` 结尾) 放入 `video_config.MANUAL_IMPORT_DIR` 指定的目录中。
    *   运行导入脚本:
        ```bash
        python video_retrieval/video_retrieval_main.py import_manual_videos
        # 或指定自定义源目录
        # python video_retrieval/video_retrieval_main.py import_manual_videos --manual_dir /path/to/your/other_manual_videos
        ```

3.  **处理与切分视频 (Phase 0)**:
    *   处理单个视频:
        ```bash
        python video_retrieval/video_retrieval_main.py process_long_video --video_path "/path/to/your/long_video.mp4"
        ```
    *   处理指定目录下的所有视频:
        ```bash
        python video_retrieval/video_retrieval_main.py process_long_video --video_dir "/path/to/your/long_videos_folder"
        # 默认会处理 video_config.LONG_VIDEOS_DIR 下的视频
        # python video_retrieval/video_retrieval_main.py process_long_video --video_dir data/long_videos 
        ```
    *   处理数据库中所有未处理的视频 (通过下载或手动导入但未被`process_long_video`处理的):
        ```bash
        python video_retrieval/video_retrieval_main.py process_long_video --process_all_new_in_db
        ```

4.  **构建/更新索引 (Phase 1)**:
    对 `video_config.CLIPS_DIR` 中的所有片段（或其中尚未建立索引的片段）进行特征提取和索引构建。
    ```bash
    python video_retrieval/video_retrieval_main.py build_index
    # 强制重新索引所有片段:
    python video_retrieval/video_retrieval_main.py build_index --force
    ```

5.  **检索视频 (Phase 2)**:
    ```bash
    python video_retrieval/video_retrieval_main.py search --query "日落下的海滩" --top_n 5
    ```

## 配置说明

-   主要的配置项位于 `video_config.py`，包括各种路径、模型名称、API参数、切分和索引的行为参数。
-   `video_config.ensure_directories()` 会在程序启动时自动创建所需的目录结构和 `keywords.txt` 占位文件。

## 注意事项

-   首次运行或使用新模型时，Hugging Face Transformers 和 Whisper 模型会自动下载，可能需要一些时间。
-   确保 FFmpeg 已正确安装并位于系统 PATH 中。
-   系统默认会尝试使用 Apple Silicon (MPS) 进行 PyTorch 计算，如果失败或不可用，会自动回退到 CPU。FAISS 和 SentenceTransformer 通常在 CPU 上运行以保证稳定性。
-   API密钥的保管和使用请遵守相关服务提供商的条款。 