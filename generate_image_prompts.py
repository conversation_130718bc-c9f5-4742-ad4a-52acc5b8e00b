#!/usr/bin/env python3

import sys
import os
import json
import argparse
import re
from config import logger
from modules.gpt4_interface import call_gpt_json_response
from typing import List, Dict, Tuple, Any, Optional, Union  # 添加 Union 到导入列表

# 保留必要的常量
MANGA_STYLE_CONFIG = {
    "manga_style": "Silent manga style - no dialogue text, emotions conveyed through expressions and mood bubbles only",
    "bubble_usage": "Use mood bubbles to express emotions and reactions without text",
    "visual_emphasis": "Focus on facial expressions, body language, and environmental storytelling"
}

ADD_STYLE = False

def load_config(config_file: str) -> Dict:
    """加载配置文件"""
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
            
        # 验证必要的配置字段
        required_fields = {"background", "characters"}
        if not all(field in config for field in required_fields):
            logger.error(f"配置文件缺少必要字段: {required_fields - set(config.keys())}")
            raise ValueError("配置文件格式错误")
            
        return config
    except Exception as e:
        logger.error(f"加载配置文件失败: {str(e)}")
        raise

def load_episode(episode_file: str) -> Dict:
    """加载剧集文件"""
    try:
        with open(episode_file, 'r', encoding='utf-8') as f:
            episode = json.load(f)
            
        # 添加数据验证和日志
        logger.debug(f"加载的剧集数据结构: {episode.keys()}")
        
        # 将数据转换为标准格式
        standardized_episode = {
            "episode_number": episode.get("n", "Unknown"),
            "title": episode.get("t", "Unknown Episode"),
            "scenes": []
        }
        
        # 转换场景数据
        for scene in episode.get("s", []):
            standardized_scene = {
                "scene_number": scene.get("n", 0),
                "scene_description": scene["environment"]["image"],
                "narration": scene["narration"]["nr"],
                "dialogues": []
            }
            
            # 转换对话数据
            if "dialogue" in scene:
                for dialogue in scene["dialogue"]:
                    standardized_dialogue = {
                        "character": dialogue.get("c", ""),
                        "emotion": dialogue.get("m", ""),
                        "content": dialogue.get("t", "")
                    }
                    standardized_scene["dialogues"].append(standardized_dialogue)
            
            standardized_episode["scenes"].append(standardized_scene)
            
        return standardized_episode
        
    except Exception as e:
        logger.error(f"加载剧集文件失败: {str(e)}")
        raise

def create_character_mapping(config: Dict) -> Dict[str, Dict]:
    """创建角色名称(包括别名)到外观信息的映射"""
    mapping = {}
    logger.debug("开始创建角色映射...")
    
    for char_name, char_info in config["characters"].items():
        # 确保外观信息存在
        appearance = char_info.get("appearance", {})
        if not appearance:
            continue
        
        # 添加主名称映射
        normalized_name = char_name.lower().strip()
        mapping[normalized_name] = appearance
        #logger.debug(f"添加主角色映射: '{char_name}' -> '{normalized_name}'")
        
        # 添加所有别名的映射
        for alias in char_info.get("aliases", []):
            normalized_alias = alias.lower().strip()
            mapping[normalized_alias] = appearance
            #logger.debug(f"添加别名映射: '{alias}' -> '{normalized_alias}' (对应角色: {char_name})")
    
    #logger.debug(f"完成角色映射创建，共 {len(mapping)} 个映射项")
    #logger.debug(f"所有可用的映射键: {list(mapping.keys())}")
    
    return mapping

def prepare_scene_text(scene: Dict, config: Dict) -> Tuple[str, Dict]:
    """准备场景文本和上下文"""
    text_parts = []
    
    # 添加场景描述
    if "scene_description" in scene:
        text_parts.append(f"Scene: {scene['scene_description']}")
    elif "scene_summary" in scene:
        text_parts.append(f"Scene: {scene['scene_summary']}")
        
    # 添加叙述
    if "narration" in scene:
        text_parts.append(f"Narration: {scene['narration']}")
    
    # 添加对话
    if scene.get("dialogues"):
        dialogue_parts = []
        for dialogue in scene["dialogues"]:
            if "character" in dialogue and "content" in dialogue:
                emotion = f" ({dialogue['emotion']})" if "emotion" in dialogue else ""
                dialogue_parts.append(f"{dialogue['character']}{emotion}: {dialogue['content']}")
        if dialogue_parts:
            text_parts.append("Dialogues: " + " | ".join(dialogue_parts))
    
    # 合并所有文本
    scene_text = "\n".join(filter(None, text_parts))
    
    # 准备全局上下文
    global_context = {
        "time_period": config["background"]["time_period"],
        "location_setting": config["background"]["location_setting"],
        "cultural_style": config["background"]["cultural_style"],
        "clothing_style": config["background"].get("clothing_style", ""),
        "architectural_style": config["background"]["architectural_style"],
        "technology_level": config["background"]["technology_level"],
        "overall_tone": config["background"]["overall_tone"],
        # 添加角色描述
        "character_descriptions": {
            name: info["appearance"] 
            for name, info in config["characters"].items()
        }
    }
    
    return scene_text, global_context

def generate_prompt_with_gpt4(text: str, context: Dict[str, str], prompt_type: str = "image") -> Dict:
    """生成场景提示"""
    try:
        # 准备GPT请求数据
        data = {
            "text": text,
            "context": context
        }
        
        # 根据prompt_type选择不同的提示模板
        template = {
            "manga": "generate_manga_prompt",
            "image": "generate_image_prompt", 
            "manga_images": "generate_manga_images"
        }.get(prompt_type)
        
        # 调用GPT生成提示
        response = call_gpt_json_response(
            template,
            data,
            using_cache=False
        )
        
        #logger.debug(f"从GPT获取到的响应: {response}")

        if not response:
            logger.warning("从GPT获取到空响应")
            return {}
            
        # 验证响应格式
        if prompt_type == "image":
            if "prompt" not in response:
                logger.warning("响应缺少 'prompt' 字段")
                return {}
                
            # 直接返回 prompt 内容，而不是整个 response
            prompt = response["prompt"]
            return prompt  # 直接返回 prompt 内容
        
        elif prompt_type == "manga":
            required_fields = {"narrative", "page_setup", "environment", "panels"}
            if not all(field in response for field in required_fields):
                logger.warning(f"响应缺少必需字段: {required_fields - set(response.keys())}")
                return {}
                
        elif prompt_type == "manga_images":
            required_fields = {"total_panels", "panels"}
            if not all(field in response for field in required_fields):
                logger.warning(f"响应缺少必需字段: {required_fields - set(response.keys())}")
                return {}
        
        return response
        
    except Exception as e:
        logger.warning(f"GPT-4 提示生成失败: {e}")
        return {}

def standardize_prompt_characters(prompt: Dict, char_mapping: Dict[str, Dict]) -> Dict:
    """标准化提示中的角色信息"""
    #logger.debug(f"开始标准化角色信息，提示类型: {type(prompt)}")
    
    def process_character(char: Dict) -> Dict:
        """处理单个角色信息"""
        # 添加数据类型检查
        if not isinstance(char, dict):
            logger.warning(f"无效的角色数据格式: {char}")
            return {}
            
        # 添加名称验证
        char_name = char.get("name", "")
        if not char_name:
            logger.warning("角色数据缺少名称")
            return char
            
        char_name_lower = char_name.lower().strip()
        logger.info(f"正在查找角色外观信息 - 原始名称: '{char_name}', 标准化名称: '{char_name_lower}'")
        
        if char_name_lower in char_mapping:
            char_info = char_mapping[char_name_lower]
            standardized_char = {
                "name": char_name,
                "pose": char.get("pose", ""),
                "expression": char.get("expression", ""),
                "interaction": char.get("interaction", ""),
                "appearance": char_info
            }
            logger.debug(f"成功匹配角色 '{char_name}' 的外观信息")
            return standardized_char
        else:
            logger.warning(f"❌ 未找到角色映射: '{char_name}' (标准化后: '{char_name_lower}')")
            return char

    def process_characters_section(characters_data: Union[List, Dict]) -> Union[List, Dict]:
        """处理角色部分的数据"""
        # 添加详细的数据结构日志
        #logger.debug(f"处理角色数据: {json.dumps(characters_data, ensure_ascii=False)}")
        
        # 处理包含 character 字段的字典格式
        if isinstance(characters_data, dict) and "character" in characters_data:
            result = characters_data.copy()
            result["character"] = [process_character(char) for char in characters_data["character"]]
            return result
            
        # 处理列表格式
        elif isinstance(characters_data, list):
            return [process_character(char) for char in characters_data]
            
        # 处理单个角色字典
        elif isinstance(characters_data, dict) and "name" in characters_data:
            return process_character(characters_data)
            
        logger.warning(f"未知的角色数据格式: {characters_data}")
        return characters_data

    # 处理不同类型的提示结构
    if isinstance(prompt, dict):
        # manga_images 类型
        if "panels" in prompt:
            for panel in prompt["panels"]:
                if "characters" in panel:
                    panel["characters"] = process_characters_section(panel["characters"])
        
        # image 类型
        elif "prompt" in prompt:
            for section in ["scene", "narrative"]:
                if section in prompt["prompt"] and "characters" in prompt["prompt"][section]:
                    prompt["prompt"][section]["characters"] = process_characters_section(
                        prompt["prompt"][section]["characters"]
                    )
        
        # manga 类型
        elif any(key in prompt for key in ["narrative", "page_setup", "environment"]):
            for section in ["narrative", "environment"]:
                if section in prompt and "characters" in prompt[section]:
                    prompt[section]["characters"] = process_characters_section(
                        prompt[section]["characters"]
                    )
    
    #logger.debug(f"可用的角色映射: {list(char_mapping.keys())}")
    
    return prompt

def process_episode(
    config: Dict,
    episode: Dict,
    output_file: str,
    prompt_type: str = "manga_images"
) -> None:
    """处理剧集并生成图像提示"""
    try:
        # 创建角色映射表
        char_mapping = create_character_mapping(config)
        logger.info(f"创建角色映射表，包含 {len(char_mapping)} 个名称映射")
        
        # 处理每个场景
        for scene in episode["scenes"]:
            # 准备场景文本和上下文
            scene_text, global_context = prepare_scene_text(scene, config)
            
            # 生成基础提示
            result = generate_prompt_with_gpt4(
                text=scene_text,
                context=global_context,
                prompt_type=prompt_type
            )
            
            if result:
                # 标准化提示中的角色信息
                standardized_result = standardize_prompt_characters(result, char_mapping)
                scene["prompt"] = standardized_result
                logger.debug(f"成功为场景 {scene['scene_number']} 生成标准化提示")
            else:
                logger.warning(f"场景 {scene['scene_number']} 生成提示失败")
                scene["prompt"] = {}
        
        # 保存结果
        output_data = {
            "episode_number": episode["episode_number"],
            "title": episode["title"],
            "scenes": episode["scenes"]
        }
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # 保存到文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)
            
        prompt_type_str = "漫画分镜" if prompt_type == "manga" else "图像生成"
        logger.info(f"成功为 {len(episode['scenes'])} 个场景生成{prompt_type_str}提示，已保存到 {output_file}")
        
    except Exception as e:
        logger.error(f"处理剧集时出错: {e}", exc_info=True)
        raise

def parse_arguments() -> argparse.Namespace:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='从剧集文件生成图像提示。',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    parser.add_argument(
        '--config',
        required=True,
        help='配置文件路径，包含背景和角色息'
    )
    
    parser.add_argument(
        '--episode',
        required=True,
        help='剧集文件路径，包含场景信息'
    )
    
    parser.add_argument(
        '-o', '--output',
        help='输出JSON文件的路径。默认为剧集文件名_prompts.json'
    )
    
    parser.add_argument(
        '--prompt',
        choices=['manga', 'image', 'manga_images'],
        default='image',
        help='提示生成类型：漫画分镜(manga)、图像生成(image)或漫画图像(manga_images)'
    )

    # 获取参数
    args = parser.parse_args()

    # 修改输出路径的处理
    if not args.output:
        episode_filename = os.path.splitext(os.path.basename(args.episode))[0]
        # 使用config文件所在的目录
        config_dir = os.path.dirname(os.path.abspath(args.config))
        output_dir = os.path.join(config_dir, episode_filename)
        os.makedirs(output_dir, exist_ok=True)
        args.output = os.path.join(output_dir, f"{episode_filename}_prompts.json")
        logger.info(f"将使用默认输出路径: {args.output}")

    return args

def main():
    """主函数"""
    try:
        # 解析命令行参数
        args = parse_arguments()
        
        # 加载配置和剧集文件
        config = load_config(args.config)
        episode = load_episode(args.episode)
        
        # 处理剧集
        process_episode(
            config=config,
            episode=episode,
            output_file=args.output,
            prompt_type=args.prompt
        )
        
    except Exception as e:
        logger.error(f"处理过程中出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()