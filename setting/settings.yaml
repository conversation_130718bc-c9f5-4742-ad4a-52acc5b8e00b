languages: 
- Portuguese
- Spanish
- Japanese
USING_CACHE: True

BATCH_SIZE: 10
MAX_TOKENS: 8000
MIN_SUMMARY_LENGTH: 2000
INDEXES_PATH: data/indexes
RAW_TEXT_PATH: data/raw_text
PROCESSED_TEXT_PATH: data/processed_text
EMBEDDINGS_PATH: data/embeddings
SUMMARIES_PATH: data/summaries
LOCALIZED_TEXT_PATH: data/localized_text
TRANSLATIONS_PATH: data/translations
EPISODES_PATH: data/episodes
TTS_SCRIPTS_PATH: data/tts_scripts
AUDIO_FILES_PATH: data/audio_files
LOGS_PATH: data/logs
GPT4_CACHE_DIR: data/cache/gpt4_responses
GPT4_CACHE_EXPIRY_DAYS: 14
SUMMARY_LENGTH: 800
SUMMARY_STYLE: engaging
COMPRESSION_RATIO: 0.3
LANGUAGE: Chinese
AUDIO_CACHE_DIR: data/audio_cache
# LLM 模型配置
# 可选值: azure, openai, anthropic, deepseek-openai
LLM_TYPE: azure
#LLM_TYPE: google

# 模型键名配置
# 对于 azure: gpt4, o1, o3-mini
# 对于 openai: gpt4, gpt4o, gpt35
# 对于 anthropic: claude3-opus, claude3-sonnet, claude3-haiku, claude2
# 对于 deepseek-openai: deepseek-reasoner, deepseek-chat, deepseek-coder (通过 OpenAI 兼容接口)
#MODEL_KEY: o4-mini
#MODEL_KEY: gemini-2.5-pro-preview-03-25
MODEL_KEY: gpt-4.1
#MODEL_KEY: deepseek-reasoner
#MODEL_KEY: ep-20250226164618-9cq2r
# DeepSeek API 配置
# DEEPSEEK_API_KEY: 在环境变量中设置
# DEEPSEEK_API_BASE: https://api.deepseek.com/v1 (默认值，可在环境变量中覆盖)

# 图像生成配置
IMAGE_GEN:
  SERVER_ADDRESS: "127.0.0.1:8188"
  WORKFLOW_PATH: "assets/workflow/storytelling-without-upscale.json"

AVATAR_GEN:
  EN_AVATAR_PATH: "assets/avatars/en_story_avatar"