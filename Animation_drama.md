# 动画剧本生成系统完整流程

## 概述

本系统实现从小说文本到动画视频的完整自动化流程，包括剧本改编、语音合成、图片生成和视频制作四个主要阶段。

## 系统架构

```
小说文本 → 剧本改编 → 语音合成 → 图片生成 → 视频合成
```

## 详细流程

### 阶段1：小说改编为剧本 (Novel to Episode)

**目标**: 将长篇小说转换为结构化的剧集内容

**输入**: 
- 原始小说文本文件 (`save_witch_whole.txt`)

**处理步骤**:
1. **章节摘要生成** (`generate_summary.py`)
   - 将大型小说分割成章节
   - 使用 LLM 为每个章节生成结构化摘要
   - 提取关键情节、角色、场景信息

2. **剧集结构化** (`generate_episodes.py`)
   - 将章节摘要分组为剧集
   - 生成每集的场景结构
   - 确定角色对话和旁白内容
   - 生成场景环境描述

**输出**:
- `save_witch_whole.json` - 章节摘要
- `save_witch_whole_group.json` - 分组信息
- `save_witch_whole_story_outline.json` - 故事大纲
- `episode_XX.json` - 结构化剧本文件

**剧本JSON结构**:
```json
{
  "n": 1,
  "t": "剧集标题",
  "c": [角色列表],
  "s": [
    {
      "n": 场景编号,
      "sn": 场景组编号,
      "environment": {
        "image": "场景环境描述"
      },
      "narration": {
        "content": "旁白内容"
      },
      "dialogue": [
        {
          "c": "角色名",
          "m": "情绪",
          "t": "对话内容"
        }
      ]
    }
  ]
}
```

### 阶段2：语音合成 (Text to Speech)

**目标**: 根据剧本内容生成对应的音频文件

**输入**: 
- 结构化剧本文件 (`episode_XX.json`)
- 语音配置文件 (`voice_config.json`)

**处理工具**: `episode_tts.py`

**处理步骤**:
1. **语音配置解析**
   - 为不同角色配置专属语音
   - 设置情绪风格映射
   - 配置旁白语音

2. **文本转语音**
   - 使用 Azure TTS 服务
   - 按场景顺序处理旁白和对话
   - 生成时间轴信息

3. **音频合成**
   - 将所有音频片段按时间轴合并
   - 添加适当的停顿和过渡
   - 生成完整的剧集音频

**输出**:
- `episode_XX.wav` - 完整音频文件
- `episode_XX_timing.json` - 包含时间轴的剧本

**语音配置格式**:
```json
{
  "narration": {
    "voice": "zh-CN-YunxiNeural",
    "default_style": "narration"
  },
  "characters": {
    "角色名": {
      "voice": {
        "voice": "zh-CN-YunyangNeural",
        "default_style": "calm",
        "styles": {
          "Neutral": "calm",
          "Cheerful": "cheerful",
          "Sad": "sad",
          "Angry": "angry"
        }
      }
    }
  }
}
```

### 阶段3：图片生成 (Scene to Image)

**目标**: 为每个场景生成对应的视觉图片

**输入**: 
- 结构化剧本文件 (`episode_XX.json`)

**处理工具**: 
- `generate_image_prompts.py` - 生成图片提示词
- `generate_images.py` - 调用 ComfyUI 生成图片

**处理步骤**:
1. **提示词生成**
   - 从场景环境描述提取视觉元素
   - 结合角色信息和情节背景
   - 生成详细的图片描述提示词
   - 添加艺术风格和质量控制词

2. **图片生成**
   - 调用 ComfyUI API
   - 使用 Stable Diffusion 模型
   - 批量生成场景图片
   - 进行质量检查和筛选

3. **图片处理**
   - 调整图片尺寸和格式
   - 确保视觉一致性
   - 优化图片质量

**输出**:
- `images/episode_XX/scene_XX.png` - 场景图片文件
- 更新的剧本文件（包含图片路径信息）

**提示词生成示例**:
```
原始描述: "灰暗的天空笼罩着低矮的砖石房屋，圆形广场中央矗立着门型绞架，周围人群涌动，气氛紧张。"

生成提示词: "medieval town square, overcast sky, stone buildings, gallows in center, crowd gathering, tense atmosphere, cinematic lighting, detailed architecture, dramatic composition, high quality, 4k"
```

### 阶段4：视频合成 (Image + Audio to Video)

**目标**: 将图片和音频合成为最终的动画视频

**输入**: 
- 时间轴剧本文件 (`episode_XX_timing.json`)
- 场景图片文件
- 音频文件 (`episode_XX.wav`)

**处理工具**: 
- `generate_video.py` - 视频合成主程序
- `images2video.py` - 图片动效处理
- `image2clip.py` - 图片视频转换

**处理步骤**:
1. **图片动效处理**
   - 为静态图片添加动态效果
   - 支持 Pan/Zoom/DepthFlow 等效果
   - 根据场景内容选择合适的动效

2. **音视频同步**
   - 根据时间轴信息同步图片和音频
   - 确保场景切换与音频内容匹配
   - 处理场景间的过渡效果

3. **视频合成**
   - 将所有场景片段合并
   - 添加转场效果
   - 生成最终视频文件

**输出**:
- `episode_XX_video.mp4` - 最终动画视频

## 技术依赖

### 必需服务
- **Azure Speech Services** - 用于文本转语音
- **ComfyUI** - 用于图片生成
- **FFmpeg** - 用于视频处理

### Python 包依赖
```
azure-cognitiveservices-speech
pydub
PIL (Pillow)
requests
opencv-python
moviepy
numpy
```

### 环境变量配置
```bash
# Azure TTS 配置
AZURE_SUBSCRIPTION_KEY=your_azure_key
AZURE_REGION=your_azure_region

# ComfyUI 配置
COMFYUI_URL=http://127.0.0.1:8188
```

## 使用方法

### 1. 环境准备
```bash
# 安装依赖
pip install -r requirements.txt

# 配置环境变量
export AZURE_SUBSCRIPTION_KEY="your_key"
export AZURE_REGION="your_region"

# 启动 ComfyUI 服务
cd ComfyUI && python main.py
```

### 2. 准备配置文件
```bash
# 复制并修改语音配置
cp voice_config_example.json voice_config.json
# 根据需要修改角色语音配置
```

### 3. 运行完整流程
```bash
python generate_animation_drama.py \
    "2-animation-drama/raw_text/save_witch_whole.txt" \
    "save_witch_whole" \
    --voice_config "voice_config.json"
```

### 4. 分阶段执行
```bash
# 只生成剧本
python generate_animation_drama.py \
    "novel.txt" "project_name" \
    --voice_config "voice_config.json" \
    --stages summary episodes

# 只生成音频
python generate_animation_drama.py \
    "novel.txt" "project_name" \
    --voice_config "voice_config.json" \
    --stages audio

# 只生成图片
python generate_animation_drama.py \
    "novel.txt" "project_name" \
    --voice_config "voice_config.json" \
    --stages images

# 只生成视频
python generate_animation_drama.py \
    "novel.txt" "project_name" \
    --voice_config "voice_config.json" \
    --stages videos
```

## 输出文件结构

```
2-animation-drama/
├── raw_text/
│   ├── save_witch_whole.txt              # 原始小说
│   ├── save_witch_whole.json             # 章节摘要
│   ├── save_witch_whole_group.json       # 分组信息
│   └── save_witch_whole_story_outline.json # 故事大纲
└── episodes/
    └── save_witch_whole/
        ├── generation_state.json         # 生成状态
        ├── episode_01.json              # 剧本文件
        ├── episode_01.wav               # 音频文件
        ├── episode_01_timing.json       # 时间轴文件
        ├── episode_01_video.mp4         # 视频文件
        ├── episode_02.json
        ├── episode_02.wav
        ├── episode_02_timing.json
        ├── episode_02_video.mp4
        └── images/                      # 图片目录
            ├── episode_01/
            │   ├── scene_01.png
            │   ├── scene_02.png
            │   └── ...
            └── episode_02/
                ├── scene_01.png
                └── ...
```

## 质量控制

### 剧本质量
- 确保场景描述详细且具有视觉表现力
- 角色对话自然流畅
- 情节连贯性检查

### 语音质量
- 角色语音风格一致性
- 情绪表达准确性
- 音频清晰度和音量平衡

### 图片质量
- 视觉风格统一
- 场景描述匹配度
- 图片分辨率和质量

### 视频质量
- 音画同步准确性
- 场景切换流畅性
- 整体观看体验

## 故障排除

### 常见问题
1. **Azure TTS 认证失败** - 检查环境变量配置
2. **ComfyUI 连接失败** - 确认服务运行状态
3. **图片生成失败** - 检查提示词格式和模型加载
4. **视频合成错误** - 验证 FFmpeg 安装和文件路径

### 性能优化
1. **并行处理** - 多个剧集可以并行生成
2. **缓存机制** - 避免重复生成已存在的内容
3. **资源管理** - 监控 GPU 内存使用情况
4. **批量处理** - 图片生成使用批量模式

## 扩展功能

### 未来改进方向
1. **多语言支持** - 支持其他语言的语音合成
2. **风格定制** - 支持不同的艺术风格
3. **交互式编辑** - 提供图形界面进行内容调整
4. **质量评估** - 自动评估生成内容的质量
5. **云端部署** - 支持云端批量处理
